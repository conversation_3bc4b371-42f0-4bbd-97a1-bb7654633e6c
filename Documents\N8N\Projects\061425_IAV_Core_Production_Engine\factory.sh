#!/usr/bin/env bash
# ===================================================================
# IAV VIRAL CLIP FACTORY - MASTER CONTROL SCRIPT
# ===================================================================
# This script provides a unified interface for managing the entire
# IAV Viral Clip Factory system.

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="IAV Viral Clip Factory"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yaml"
CONFIG_FILE="$SCRIPT_DIR/config.env"
ENV_FILE="$SCRIPT_DIR/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_header "Checking Prerequisites"
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    # Check if config files exist
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file not found: $ENV_FILE"
        log_info "Creating default .env file..."
        cat > "$ENV_FILE" << EOF
# Docker Compose Environment Variables
UID=1000
GID=1000
COMPOSE_PROJECT_NAME=iav_viral_factory
EOF
        log_success "Created default .env file"
    fi
    
    log_success "All prerequisites met"
}

# Get Docker Compose command
get_compose_cmd() {
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
    else
        echo "docker compose"
    fi
}

# Start the factory
start_factory() {
    log_header "Starting $PROJECT_NAME"
    
    check_prerequisites
    
    local compose_cmd
    compose_cmd=$(get_compose_cmd)
    
    log_info "Building and starting services..."
    $compose_cmd -f "$COMPOSE_FILE" up --build -d
    
    log_info "Waiting for services to become healthy..."
    sleep 30
    
    # Check service health
    log_info "Checking service health..."
    $compose_cmd -f "$COMPOSE_FILE" ps
    
    # Pull Ollama models if needed
    log_info "Ensuring AI models are available..."
    if ! docker exec iav_ollama ollama list | grep -q "llama3.1:8b"; then
        log_info "Pulling LLaMA model (this may take a while)..."
        docker exec iav_ollama ollama pull llama3.1:8b-instruct-q4_K_M
    fi
    
    log_success "$PROJECT_NAME started successfully!"
    log_info "Access the web interface at: http://localhost:8601"
    log_info "Access n8n interface at: http://localhost:8678"
    log_info "Ollama API available at: http://localhost:11534"
}

# Stop the factory
stop_factory() {
    log_header "Stopping $PROJECT_NAME"
    
    local compose_cmd
    compose_cmd=$(get_compose_cmd)
    
    log_info "Stopping all services..."
    $compose_cmd -f "$COMPOSE_FILE" down
    
    log_success "$PROJECT_NAME stopped successfully!"
}

# Show logs
show_logs() {
    log_header "Showing $PROJECT_NAME Logs"
    
    local compose_cmd
    compose_cmd=$(get_compose_cmd)
    
    local service="${1:-}"
    
    if [ -n "$service" ]; then
        log_info "Showing logs for service: $service"
        $compose_cmd -f "$COMPOSE_FILE" logs -f "$service"
    else
        log_info "Showing logs for all services (press Ctrl+C to exit)"
        $compose_cmd -f "$COMPOSE_FILE" logs -f
    fi
}

# Show status
show_status() {
    log_header "$PROJECT_NAME Status"
    
    local compose_cmd
    compose_cmd=$(get_compose_cmd)
    
    # Service status
    echo -e "${CYAN}Service Status:${NC}"
    $compose_cmd -f "$COMPOSE_FILE" ps
    echo
    
    # Resource usage
    echo -e "${CYAN}Resource Usage:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" \
        $(docker ps --filter "label=com.docker.compose.project=iav_viral_factory" --format "{{.Names}}") 2>/dev/null || true
    echo
    
    # Disk usage
    echo -e "${CYAN}Data Directory Usage:${NC}"
    if [ -d "$SCRIPT_DIR/data" ]; then
        du -sh "$SCRIPT_DIR/data" 2>/dev/null || echo "Unable to calculate disk usage"
    else
        echo "Data directory not found"
    fi
    echo
    
    # Queue status (if Redis is running)
    echo -e "${CYAN}Job Queue Status:${NC}"
    if docker exec iav_redis redis-cli ping &>/dev/null; then
        local queue_length
        queue_length=$(docker exec iav_redis redis-cli llen iav_job_queue 2>/dev/null || echo "0")
        echo "Jobs in queue: $queue_length"
    else
        echo "Redis not available"
    fi
}

# Restart the factory
restart_factory() {
    log_header "Restarting $PROJECT_NAME"
    
    stop_factory
    sleep 5
    start_factory
}

# Clean up everything (DESTRUCTIVE)
clean_factory() {
    log_header "Cleaning $PROJECT_NAME (DESTRUCTIVE OPERATION)"
    
    echo -e "${RED}WARNING: This will remove all containers, volumes, and data!${NC}"
    echo -e "${RED}This action cannot be undone!${NC}"
    echo
    read -p "Are you sure you want to continue? Type 'yes' to confirm: " -r
    
    if [[ ! $REPLY =~ ^yes$ ]]; then
        log_info "Operation cancelled"
        return 0
    fi
    
    local compose_cmd
    compose_cmd=$(get_compose_cmd)
    
    log_warning "Stopping and removing all services..."
    $compose_cmd -f "$COMPOSE_FILE" down --volumes --remove-orphans
    
    log_warning "Removing data directories..."
    rm -rf "$SCRIPT_DIR/data" "$SCRIPT_DIR/db_data" "$SCRIPT_DIR/redis_data" \
           "$SCRIPT_DIR/n8n_data" "$SCRIPT_DIR/ollama_data"
    
    log_warning "Pruning Docker system..."
    docker system prune -f
    
    log_success "Factory cleaned successfully!"
}

# Show help
show_help() {
    cat << EOF
${PURPLE}IAV Viral Clip Factory - Master Control Script${NC}

${CYAN}USAGE:${NC}
    $0 <command> [options]

${CYAN}COMMANDS:${NC}
    ${GREEN}start${NC}           Start the entire factory system
    ${GREEN}stop${NC}            Stop all factory services
    ${GREEN}restart${NC}         Restart the factory system
    ${GREEN}status${NC}          Show system status and resource usage
    ${GREEN}logs${NC} [service]  Show logs (optionally for specific service)
    ${GREEN}clean${NC}           Clean up everything (DESTRUCTIVE)
    ${GREEN}help${NC}            Show this help message

${CYAN}SERVICES:${NC}
    postgres         PostgreSQL database
    redis            Redis cache and job queue
    ollama           AI models (LLaMA, Whisper)
    n8n              Workflow engine
    whisper          Transcription service
    frontend         Web interface

${CYAN}EXAMPLES:${NC}
    $0 start                    # Start the factory
    $0 logs n8n                 # Show n8n logs
    $0 status                   # Check system status
    $0 stop                     # Stop everything

${CYAN}ACCESS POINTS:${NC}
    Web Interface:  http://localhost:8601
    n8n Interface:  http://localhost:8678
    Ollama API:     http://localhost:11534

For more information, see the project documentation.
EOF
}

# Main script logic
main() {
    case "${1:-help}" in
        start)
            start_factory
            ;;
        stop)
            stop_factory
            ;;
        restart)
            restart_factory
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "${2:-}"
            ;;
        clean)
            clean_factory
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: ${1:-}"
            echo
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
