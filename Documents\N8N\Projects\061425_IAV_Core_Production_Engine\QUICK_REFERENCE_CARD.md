# 🏭 IAV Core Production Engine - Viral Clip Factory - Quick Reference Card

## 🚀 Essential Commands

### Daily Operations
```bash
# Process a video
curl -X POST "http://localhost:5678/webhook/iav" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=VIDEO_ID"}'

# Check system status
docker-compose ps

# Monitor processing
docker logs -f 061425_iav_core_production_engine-n8n-1

# Find today's clips
ls data/$(date +%Y-%m-%d)/*/mp4/
```

### Troubleshooting
```bash
# Restart n8n (fixes webhook issues)
docker-compose restart n8n

# Check GPU usage
nvidia-smi

# Test webhook registration
curl http://localhost:5678/webhook-test/iav

# View recent logs
docker logs --tail 20 061425_iav_core_production_engine-n8n-1
```

### Search & Management
```bash
# Search clips by keyword
./scripts/findclip "funny"

# Show statistics
./scripts/findclip -s

# Find clips by date
./scripts/findclip -d 2025-06-16

# Check disk usage
du -sh data/
```

## 🎯 Expected Results

### Processing Time
- **5-15 min video**: 3-8 minutes
- **15-45 min video**: 8-20 minutes
- **45+ min video**: 20-45 minutes

### Output Format
- **Resolution**: 1080x1920 (vertical)
- **Format**: MP4 with H.264
- **Audio**: AAC 192kbps
- **Subtitles**: Embedded with styling
- **Count**: 5 clips per video

### File Locations
```
data/YYYY-MM-DD/IAV_Core_Production_Engine/
├── src/source_video.mp4          # Original download
├── srt/source_video.srt          # Full transcript
├── mp4/clip_1_title.mp4          # Final clips ⭐
├── mp4/clip_2_title.mp4
├── mp4/clip_3_title.mp4
├── mp4/clip_4_title.mp4
├── mp4/clip_5_title.mp4
└── manifest.jsonl                # Metadata
```

## 🔧 Common Issues & Fixes

### "Webhook not registered"
```bash
docker-compose restart n8n
# Wait 30 seconds, then test
```

### "API key expired"
1. Open http://localhost:5678
2. Settings → API Keys → Create key
3. Update config.env with new key

### "GPU not detected"
```bash
# Check drivers
nvidia-smi

# Verify Docker GPU support
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### "Download failed"
- Check internet connection
- Verify YouTube URL is accessible
- Some videos may be geo-restricted

### "Out of memory"
- Reduce video length (max 60 minutes recommended)
- Increase Docker memory limits
- Check available disk space

## 📊 Performance Monitoring

### Resource Targets
- **GPU Usage**: NVENC ≤45% during encoding
- **CPU Usage**: Whisper ~250% (multi-core)
- **VRAM Usage**: LLaMA ~3.8GB
- **Processing Success**: >95%

### Health Checks
```bash
# Container health
docker-compose ps | grep healthy

# Resource usage
docker stats --no-stream

# GPU monitoring
nvidia-smi dmon -s u -c 5

# Disk space
df -h data/
```

## 🎬 Framing Modes

### Automatic Detection
- **Native**: Vertical videos (≤0.6 aspect ratio) → scale to 1080x1920
- **Center-crop**: Landscape videos → crop center to 9:16 ratio
- **Split-stack**: Manual override with `?frame=stack` parameter

### Force Split-Stack Mode
```bash
curl -X POST "http://localhost:5678/webhook/iav?frame=stack" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=VIDEO_ID"}'
```

## 🔄 Maintenance Schedule

### Daily (2 minutes)
- Check system status: `docker-compose ps`
- Process new videos via webhook
- Review generated clips

### Weekly (10 minutes)
- Check disk usage: `du -sh data/`
- Review processing logs for errors
- Test webhook functionality

### Monthly (30 minutes)
- Update yt-dlp: `docker-compose build --no-cache n8n`
- Rotate API keys for security
- Backup important clips
- Review performance metrics

## 🆘 Emergency Recovery

### Complete System Reset
```bash
# Stop all services
docker-compose down

# Clean everything (CAUTION: loses data)
docker system prune -a
rm -rf n8n_data/ db_data/ redis_data/

# Redeploy
./deploy.sh
```

### Quick Recovery (preserves data)
```bash
# Restart services
docker-compose restart

# Re-import workflow if needed
# 1. Open http://localhost:5678
# 2. Import workflow.json via UI
# 3. Activate workflow
```

## 📱 Platform Upload

### TikTok Studio
1. Open `/data/YYYY-MM-DD/PROJECT/mp4/`
2. Drag clips directly into TikTok Studio
3. Clips are pre-formatted (1080x1920, subtitles embedded)

### Instagram Reels
1. Transfer clips to phone or use desktop uploader
2. No additional editing needed
3. Subtitles are already embedded

### YouTube Shorts
1. Upload directly to YouTube Studio
2. Clips are optimized for Shorts format
3. Consider adding custom thumbnails

## 🎯 Success Indicators

### Green Light ✅
- HTTP 200 response from webhook
- 5 clips generated in mp4/ folder
- manifest.jsonl has 5 entries
- Clips are 30-90 seconds each
- GPU encoder utilization >0% during processing

### Red Flag ❌
- HTTP 4xx/5xx errors
- Empty mp4/ folder after processing
- Clips are 0 bytes or corrupted
- Processing takes >2x expected time
- Repeated "webhook not registered" errors

---

*Keep this reference card handy for daily operations. For detailed troubleshooting, refer to the complete guide.*
