# 🏭 IAV Viral Clip Factory

**Transform YouTube videos into viral clips automatically with AI-powered content analysis and professional video processing.**

[![Docker](https://img.shields.io/badge/Docker-Ready-blue?logo=docker)](https://docker.com)
[![n8n](https://img.shields.io/badge/n8n-Workflow-orange?logo=n8n)](https://n8n.io)
[![Streamlit](https://img.shields.io/badge/Streamlit-Frontend-red?logo=streamlit)](https://streamlit.io)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🎯 What This Does

The IAV Viral Clip Factory is a fully automated, 24/7 video processing system that:

- **Accepts YouTube URLs** through a beautiful web interface
- **Downloads videos** with intelligent retry logic and validation
- **Transcribes audio** using Whisper Large-v3 for perfect accuracy
- **Analyzes content** with LLaMA 3.1 AI to find viral moments
- **Generates clips** in perfect 9:16 format with embedded subtitles
- **Organizes output** in searchable, date-based directory structure
- **Monitors progress** with real-time status dashboard and analytics

## 🚀 Quick Start

### Prerequisites

- **Docker Desktop** with Docker Compose
- **NVIDIA GPU** with CUDA support (8GB+ VRAM recommended)
- **16GB+ RAM** (32GB recommended)
- **50GB+ free disk space**

### First-Time Setup

1. **Clone and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd IAV_Core_Production_Engine
   ```

2. **Configure user permissions:**
   ```bash
   # Get your user ID and group ID
   id -u  # Note this number
   id -g  # Note this number
   
   # Edit .env file with your values
   echo "UID=$(id -u)" > .env
   echo "GID=$(id -g)" >> .env
   echo "COMPOSE_PROJECT_NAME=iav_viral_factory" >> .env
   ```

3. **Review configuration:**
   ```bash
   # Edit config.env with your preferences
   nano config.env
   ```

4. **Start the factory:**
   ```bash
   ./factory.sh start
   ```

5. **Access the web interface:**
   - **Main Interface:** http://localhost:8601
   - **n8n Workflow:** http://localhost:8678
   - **Ollama API:** http://localhost:11534

## 🎮 How to Use

### Web Interface Method (Recommended)

1. **Open the factory interface:** http://localhost:8601
2. **Submit a job:**
   - Paste a YouTube URL in the input field
   - Click "🚀 Generate Clips"
   - Monitor progress in real-time
3. **Download results:**
   - Check the "Completed Jobs" tab
   - Find your clips in the specified folder
   - Upload directly to TikTok, Instagram, or YouTube Shorts

### Command Line Method

```bash
# Check system status
./factory.sh status

# View logs
./factory.sh logs

# Restart if needed
./factory.sh restart
```

## 📁 Output Structure

```
data/
└── YYYY-MM-DD_Video_Title_JobID/
    ├── src/
    │   └── source_video.mp4          # Original download
    ├── srt/
    │   └── transcript.srt             # Full transcript
    ├── mp4/                           # 🎬 FINAL CLIPS
    │   ├── clip_001_viral_moment.mp4
    │   ├── clip_002_hook_title.mp4
    │   └── ...
    ├── logs/
    │   ├── status.json                # Processing status
    │   ├── download.log               # Download logs
    │   └── error.log                  # Error logs (if any)
    ├── manifest.jsonl                 # Searchable metadata
    └── job_metadata.json              # Job information
```

## 🔧 System Architecture

### Core Services

- **Frontend (Streamlit)** - Web interface for job submission and monitoring
- **n8n** - Workflow orchestration with Redis job queue integration
- **Ollama** - Local AI models (LLaMA 3.1 8B + Whisper Large-v3)
- **PostgreSQL** - Workflow data and execution history
- **Redis** - Job queue and caching
- **Whisper ASR** - Dedicated transcription service

### Processing Pipeline

```mermaid
graph LR
    A[Web Interface] --> B[Redis Queue]
    B --> C[n8n Workflow]
    C --> D[Download Video]
    D --> E[Transcribe Audio]
    E --> F[AI Analysis]
    F --> G[Generate Clips]
    G --> H[Update Status]
    H --> I[Web Dashboard]
```

## 🛠️ Management Commands

The `factory.sh` script provides unified control:

```bash
# Start the entire system
./factory.sh start

# Check system status and resource usage
./factory.sh status

# View logs (all services or specific service)
./factory.sh logs
./factory.sh logs n8n

# Restart everything
./factory.sh restart

# Stop all services
./factory.sh stop

# Clean up everything (DESTRUCTIVE)
./factory.sh clean

# Show help
./factory.sh help
```

## 📊 Monitoring & Analytics

### Real-Time Dashboard

The web interface provides:

- **Queue Status** - Jobs waiting to be processed
- **Completed Jobs** - Successfully generated clips with metadata
- **Failed Jobs** - Error logs and troubleshooting information
- **Analytics** - Success rates, clips per job, timeline charts

### Resource Monitoring

```bash
# Check system resources
./factory.sh status

# Monitor specific service
docker logs -f iav_n8n

# Check GPU usage
nvidia-smi
```

## 🔧 Configuration

### Environment Variables

All configuration is centralized in `config.env`:

```bash
# AI Model Configuration
LLAMA_MODEL=llama3.1:8b-instruct-q4_K_M
ASR_MODEL=large-v3

# Video Processing Settings
OUTPUT_WIDTH=1080
OUTPUT_HEIGHT=1920
VIDEO_BITRATE=8M

# Factory Settings
MAX_CLIPS_PER_VIDEO=5
MAX_DOWNLOAD_RETRIES=3
DOWNLOAD_TIMEOUT=1800

# And many more...
```

### File Permissions

The system automatically handles file permissions using your host user ID:

```bash
# Your .env file should contain:
UID=1000  # Your user ID (from 'id -u')
GID=1000  # Your group ID (from 'id -g')
```

## 🚨 Error Handling & Recovery

### Automatic Recovery

- **Download Failures** - Automatic retry with exponential backoff
- **Transcription Errors** - Fallback to alternative methods
- **AI Model Timeouts** - Graceful degradation and retry logic
- **Disk Space Issues** - Automatic cleanup and alerts

### Manual Recovery

```bash
# Restart specific service
docker-compose restart n8n

# Check error logs
./factory.sh logs n8n

# Clean failed jobs
rm -rf data/factory_output/failed_jobs/*

# Full system reset (DESTRUCTIVE)
./factory.sh clean
./factory.sh start
```

## 📈 Performance Optimization

### Recommended Hardware

- **CPU:** 16+ cores for optimal transcription
- **GPU:** RTX 4080/4090 for best NVENC performance
- **RAM:** 32GB+ for large video processing
- **Storage:** NVMe SSD for faster I/O

### Performance Targets

- **Processing Time:** 5-30 minutes per video (depending on length)
- **Success Rate:** 95%+ with proper setup
- **GPU Utilization:** 80%+ during encoding
- **Clip Quality:** 1080x1920, 29.97+ fps, embedded subtitles

## 🔒 Security & Best Practices

### API Security

- Use 32-character API keys (not JWT tokens)
- Rotate keys monthly
- Monitor execution logs for anomalies

### Data Management

- Implement automated backups
- Clean source files after processing
- Monitor disk usage regularly

## 🆘 Troubleshooting

### Common Issues

**"No clips generated"**
```bash
# Check logs for errors
./factory.sh logs n8n

# Verify GPU access
docker exec iav_n8n nvidia-smi

# Check disk space
df -h
```

**"Permission denied on files"**
```bash
# Fix file permissions
sudo chown -R $(id -u):$(id -g) data/

# Update .env file
echo "UID=$(id -u)" > .env
echo "GID=$(id -g)" >> .env
```

**"Service won't start"**
```bash
# Check service health
./factory.sh status

# Restart problematic service
docker-compose restart <service-name>

# Full restart
./factory.sh restart
```

### Getting Help

1. **Check the logs:** `./factory.sh logs`
2. **Verify configuration:** Review `config.env` and `.env`
3. **Check resources:** `./factory.sh status`
4. **Restart services:** `./factory.sh restart`

## 📝 Development

### Code Quality

The project includes automated code quality tools:

- **EditorConfig** - Consistent formatting
- **Prettier** - JSON/Markdown formatting
- **ShellCheck** - Shell script linting
- **Pre-commit hooks** - Automated quality checks

```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Run quality checks
pre-commit run --all-files
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run quality checks
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **n8n** - Workflow automation platform
- **Ollama** - Local AI model hosting
- **Streamlit** - Web application framework
- **OpenAI Whisper** - Speech recognition
- **Meta LLaMA** - Large language model

---

**IAV Viral Clip Factory** - Turning long-form content into viral gold, automatically. 🎬✨
