#!/usr/bin/env bash
set -eo pipefail
echo "🛠  Building custom n8n image ..."
docker compose build n8n

echo "🚀  Starting stack ..."
docker compose up -d
echo "⏳  Waiting 60s for services ..."
sleep 60
docker compose ps

echo "📥  Pulling Ollama models (this runs once) ..."
docker exec $(docker compose ps -q ollama) ollama ls | grep -q "llama3.1" || \
  docker exec $(docker compose ps -q ollama) ollama pull llama3.1:8b-instruct-q4_K_M

echo "🏥  Running healthcheck ..."
./healthcheck.sh

echo "➡️  Seeding IAV workflow..."
if [ -f "config.env" ]; then
    source config.env
    if [ -n "$N8N_API_KEY" ] && [ "$N8N_API_KEY" != "<YOUR_KEY>" ] && [ "$N8N_API_KEY" != "your-api-key-here" ]; then
        # Check if workflow already exists
        EXISTING_WF=$(curl -s -H "X-N8N-API-Key: $N8N_API_KEY" \
                      http://localhost:5678/api/v1/workflows | grep -o '"name":"IAV Core Production Engine"' || true)

        if [ -z "$EXISTING_WF" ]; then
            echo "Importing IAV workflow..."
            IMPORT_RESPONSE=$(curl -s \
                -H "X-N8N-API-Key: $N8N_API_KEY" \
                -F "workflow=@production_workflow_final.json;type=application/json" \
                http://localhost:5678/api/v1/workflows/import)

            # Extract workflow ID using grep and sed (no jq needed)
            WF_ID=$(echo "$IMPORT_RESPONSE" | grep -o '"id":"[^"]*"' | sed 's/"id":"\([^"]*\)"/\1/')

            if [ "$WF_ID" != "null" ] && [ -n "$WF_ID" ]; then
                echo "Workflow imported with ID: $WF_ID"

                # Activate the workflow
                curl -s -X PATCH \
                    -H "X-N8N-API-Key: $N8N_API_KEY" \
                    -H "Content-Type: application/json" \
                    -d '{"active":true}' \
                    http://localhost:5678/api/v1/workflows/$WF_ID >/dev/null
                echo "Workflow $WF_ID imported & active."
            else
                echo "❌ Failed to import workflow"
            fi
        else
            echo "✅ IAV workflow already exists and is active"
        fi
    else
        echo "⚠️  No valid API key configured. Please:"
        echo "   1. Open http://localhost:5678"
        echo "   2. Go to Settings → API Keys → Create key"
        echo "   3. Copy the 32-character key"
        echo "   4. Run: echo \"N8N_API_KEY=<YOUR_KEY>\" > config.env"
        echo "   5. Re-run this script"
    fi
else
    echo "⚠️  config.env not found. Please create it with your API key."
fi

echo ""
echo "🧪  Test the pipeline with:"
echo "curl -X POST http://localhost:5678/webhook/iav \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"url\":\"https://youtu.be/dQw4w9WgXcQ\"}'"

echo "⏳  Processing … (5 min)"; sleep 300

echo "🎞  Resulting files:" && ls -lh data/clip_*.mp4 || { echo '❌  No clips found'; exit 1; }

echo "✅  MVP deployed successfully."
