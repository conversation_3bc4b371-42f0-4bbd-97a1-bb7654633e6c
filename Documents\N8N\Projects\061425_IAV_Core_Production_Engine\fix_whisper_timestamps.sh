#!/bin/bash
# Fix Whisper to properly generate SRT files with accurate timestamps

set -euo pipefail

echo "🔧 FIXING WHISPER TIMESTAMP GENERATION"
echo "===================================="

# Check if containers are running
if ! docker ps | grep -q "iav_whisper"; then
    echo "❌ Whisper container not running. Please start with: ./deploy.sh"
    exit 1
fi

if ! docker ps | grep -q "n8n"; then
    echo "❌ n8n container not running. Please start with: ./deploy.sh"
    exit 1
fi

echo ""
echo "📋 STEP 1: Testing current Whisper setup"
echo "========================================"

# Get the correct n8n container name
N8N_CONTAINER=$(docker ps --format "{{.Names}}" | grep n8n)
echo "Using n8n container: $N8N_CONTAINER"

# Copy test script to n8n container
docker cp test_whisper_timestamps.py $N8N_CONTAINER:/tmp/test_whisper.py

# Run the test
echo "🧪 Running Whisper timestamp test..."
if docker exec $N8N_CONTAINER python3 /tmp/test_whisper.py; then
    echo ""
    echo "✅ Current Whisper setup is working correctly!"
    echo "   No fix needed - timestamps are being generated properly."
    exit 0
else
    echo ""
    echo "❌ Current Whisper setup has timestamp issues."
    echo "   Proceeding with fix deployment..."
fi

echo ""
echo "📋 STEP 2: Deploying Whisper fix"
echo "================================"

# Check if we should use the wrapper or fix the existing service
read -p "Choose fix method: [1] Replace Whisper service with wrapper [2] Fix existing service [3] Skip: " choice

case $choice in
    1)
        echo "🔄 Deploying enhanced Whisper wrapper..."
        
        # Copy wrapper to n8n container
        docker cp whisper_srt_wrapper.py $N8N_CONTAINER:/tmp/whisper_wrapper.py

        # Install Flask in n8n container if not present
        docker exec $N8N_CONTAINER pip3 install --break-system-packages flask

        # Stop current whisper service
        docker stop iav_whisper || true

        # Start wrapper in n8n container
        echo "🚀 Starting enhanced Whisper wrapper..."
        docker exec -d $N8N_CONTAINER python3 /tmp/whisper_wrapper.py

        # Wait for service to start
        sleep 5

        # Test the new service
        echo "🧪 Testing enhanced wrapper..."
        if docker exec $N8N_CONTAINER python3 /tmp/test_whisper.py; then
            echo "✅ Enhanced Whisper wrapper is working!"
        else
            echo "❌ Enhanced wrapper failed. Restarting original service..."
            docker start iav_whisper
            exit 1
        fi
        ;;
        
    2)
        echo "🔧 Attempting to fix existing Whisper service..."
        
        # Check Whisper service configuration
        echo "📊 Current Whisper service info:"
        docker exec iav_whisper env | grep ASR || true
        
        # Try different API parameters
        echo "🧪 Testing with enhanced parameters..."
        
        # Create test script for enhanced parameters
        cat > /tmp/test_enhanced_whisper.py << 'EOF'
import requests
import json

def test_enhanced_whisper():
    url = "http://whisper:9000/asr"
    
    # Enhanced parameters that should force timestamps
    enhanced_params = {
        "task": "transcribe",
        "language": "en",
        "output": "srt",
        "word_timestamps": "true",
        "vad_filter": "true",
        "encode": "true",
        "timestamps": "true",
        "response_format": "srt"
    }
    
    test_file = "/data/2025-06-17_N3on Meets Young Thug/src/source_video.mp4"
    
    try:
        with open(test_file, 'rb') as f:
            files = {'audio_file': f}
            response = requests.post(url, files=files, data=enhanced_params, timeout=120)
            
        if response.status_code == 200:
            content = response.text
            if '-->' in content and ':' in content:
                print("✅ Enhanced parameters work!")
                print("Sample output:")
                print(content[:500])
                return True
            else:
                print("❌ Still no timestamps with enhanced parameters")
                return False
        else:
            print(f"❌ API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    test_enhanced_whisper()
EOF
        
        docker cp /tmp/test_enhanced_whisper.py $N8N_CONTAINER:/tmp/test_enhanced.py

        if docker exec $N8N_CONTAINER python3 /tmp/test_enhanced.py; then
            echo "✅ Enhanced parameters work! Updating workflow..."
            # The workflow will need to be updated with enhanced parameters
        else
            echo "❌ Enhanced parameters don't work. Consider option 1 (wrapper)."
            exit 1
        fi
        ;;
        
    3)
        echo "⏭️  Skipping fix deployment."
        exit 0
        ;;
        
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "📋 STEP 3: Updating workflow (if needed)"
echo "========================================"

# Check if workflow needs updating
if [ "$choice" = "2" ]; then
    echo "📝 Workflow should be updated to use enhanced parameters:"
    echo "   - Add 'word_timestamps': 'true'"
    echo "   - Add 'vad_filter': 'true'"
    echo "   - Add 'encode': 'true'"
    echo "   - Ensure 'output': 'srt'"
    echo ""
    echo "   Update the transcribe_whisper node in your workflow."
fi

echo ""
echo "📋 STEP 4: Final validation"
echo "=========================="

# Run final test
echo "🧪 Running final validation test..."
if docker exec $N8N_CONTAINER python3 /tmp/test_whisper.py; then
    echo ""
    echo "🎉 SUCCESS! Whisper timestamp generation is now working correctly."
    echo ""
    echo "📋 Next steps:"
    echo "1. Test the full IAV workflow with a video"
    echo "2. Verify clips have proper subtitle timing"
    echo "3. Check that viral moments align with actual video content"
    echo ""
    echo "🧪 Test command:"
    echo "curl -X POST http://localhost:5678/webhook/iav \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{\"url\": \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\"}'"
else
    echo ""
    echo "❌ FAILED! Whisper timestamp generation is still not working."
    echo "   Please check the logs and try manual debugging."
    exit 1
fi
