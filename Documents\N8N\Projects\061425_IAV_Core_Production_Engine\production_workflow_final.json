{"name": "IAV Core Production Engine - Viral Clip Factory", "nodes": [{"parameters": {"httpMethod": "POST", "path": "iav", "responseMode": "lastNode", "options": {}}, "name": "webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 100], "webhookId": "iav-viral-factory-2025"}, {"parameters": {"command": "=set -euo pipefail\nURL='{{$node[\"webhook\"].json[\"body\"][\"url\"]}}'\nDATE=$(date +%Y-%m-%d)\necho \"Processing URL: $URL\"\necho \"Date: $DATE\"\n\n# Extract video title and ID for folder naming\nVIDEO_INFO=$(yt-dlp --print \"%(title)s|%(id)s\" \"$URL\")\nVIDEO_TITLE=$(echo \"$VIDEO_INFO\" | cut -d'|' -f1 | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g')\nVIDEO_ID=$(echo \"$VIDEO_INFO\" | cut -d'|' -f2)\n\n# Create viral factory folder structure: YYYY-MM-DD_Video_Title\nPROJECT_FOLDER=\"${DATE}_${VIDEO_TITLE}\"\necho \"Project folder: $PROJECT_FOLDER\"\n\n# Create directory structure\nmkdir -p \"/data/$PROJECT_FOLDER/src\"\nmkdir -p \"/data/$PROJECT_FOLDER/mp4\"\nmkdir -p \"/data/$PROJECT_FOLDER/srt\"\n\necho \"Created viral factory structure for: $PROJECT_FOLDER\""}, "name": "setup_viral_factory", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [400, 100]}, {"parameters": {"command": "=set -euo pipefail\nURL='{{$node[\"webhook\"].json[\"body\"][\"url\"]}}'\nDATE=$(date +%Y-%m-%d)\n\n# Get video info again for consistency\nVIDEO_INFO=$(yt-dlp --print \"%(title)s|%(id)s\" \"$URL\")\nVIDEO_TITLE=$(echo \"$VIDEO_INFO\" | cut -d'|' -f1 | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g')\nPROJECT_FOLDER=\"${DATE}_${VIDEO_TITLE}\"\n\necho \"Downloading video to: /data/$PROJECT_FOLDER/src/source_video.mp4\"\n\n# Download with best quality, merge to mp4\nyt-dlp -f 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best' \\\n  --merge-output-format mp4 \\\n  --sponsorblock-remove all \\\n  -o \"/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n  \"$URL\"\n\necho \"Video downloaded successfully to viral factory structure\""}, "name": "download_video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [600, 100]}, {"parameters": {"command": "=set -euo pipefail\nDATE=$(date +%Y-%m-%d)\nURL='{{$node[\"webhook\"].json[\"body\"][\"url\"]}}'\n\n# Get project folder name\nVIDEO_INFO=$(yt-dlp --print \"%(title)s|%(id)s\" \"$URL\")\nVIDEO_TITLE=$(echo \"$VIDEO_INFO\" | cut -d'|' -f1 | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g')\nPROJECT_FOLDER=\"${DATE}_${VIDEO_TITLE}\"\n\necho \"Transcribing video in: $PROJECT_FOLDER\"\n\n# Use Whisper via HTTP API for transcription\ncurl -X POST \\\n  -F \"audio_file=@/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n  -F \"task=transcribe\" \\\n  -F \"language=en\" \\\n  -F \"output=srt\" \\\n  http://whisper:9000/asr \\\n  -o \"/data/$PROJECT_FOLDER/srt/transcript.srt\"\n\necho \"Transcription completed and saved to srt folder\""}, "name": "transcribe_whisper", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [800, 100]}, {"parameters": {"command": "=set -euo pipefail\nDATE=$(date +%Y-%m-%d)\nURL='{{$node[\"webhook\"].json[\"body\"][\"url\"]}}'\n\n# Get project folder name\nVIDEO_INFO=$(yt-dlp --print \"%(title)s|%(id)s\" \"$URL\")\nVIDEO_TITLE=$(echo \"$VIDEO_INFO\" | cut -d'|' -f1 | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g')\nPROJECT_FOLDER=\"${DATE}_${VIDEO_TITLE}\"\n\necho \"Analyzing transcript for viral moments in: $PROJECT_FOLDER\"\n\n# Read transcript and analyze with LLaMA\nTRANSCRIPT=$(cat \"/data/$PROJECT_FOLDER/srt/transcript.srt\")\n\n# LLaMA prompt for viral clip detection\ncurl -X POST http://ollama:11434/api/generate \\\n  -H \"Content-Type: application/json\" \\\n  -d \"{\n    \\\"model\\\": \\\"llama3.1:8b\\\",\n    \\\"prompt\\\": \\\"You are a world-class viral video producer. Your job is to find the most engaging, shareable moments that will go viral on TikTok, Instagram Reels, and YouTube Shorts. Analyze this transcript and output ONLY a JSON array of exactly 5 objects. Each object must have: start (seconds), end (seconds), title (hook description), justification (why it will go viral). Focus on: emotional peaks, controversial takes, surprising moments, quotable lines, cliffhangers. Transcript:\\\\n\\\\n$TRANSCRIPT\\\",\n    \\\"stream\\\": false\n  }\" \\\n  | jq -r '.response' > \"/data/$PROJECT_FOLDER/viral_analysis.json\"\n\necho \"Viral moment analysis completed\""}, "name": "analyze_viral_moments", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1000, 100]}, {"parameters": {"code": "// Parse LLaMA output and prepare for clip generation\nconst stdout = $input.first().json.stdout || '';\nconst DATE = new Date().toISOString().split('T')[0];\nconst URL = $('webhook').first().json.body.url;\n\n// Extract video info from URL (simplified)\nconst videoId = URL.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)?.[1] || 'unknown';\nconst videoTitle = 'Video_' + videoId; // Simplified - in production would use yt-dlp output\nconst projectFolder = `${DATE}_${videoTitle}`;\n\n// Clean and parse LLaMA JSON response\nlet rawResponse = stdout.replace(/```json|```/g, '').trim();\n// Find JSON array in response\nconst jsonMatch = rawResponse.match(/\\[.*\\]/s);\nif (!jsonMatch) {\n  throw new Error('No JSON array found in LLaMA response: ' + rawResponse);\n}\n\nlet clips = [];\ntry {\n  clips = JSON.parse(jsonMatch[0]);\n} catch (e) {\n  throw new Error('Invalid JSON from LLaMA: ' + jsonMatch[0]);\n}\n\n// Validate and prepare clips\nif (!Array.isArray(clips) || clips.length === 0) {\n  throw new Error('LLaMA did not return valid clips array');\n}\n\n// Return each clip as separate item for parallel processing\nreturn clips.slice(0, 5).map((clip, index) => ({\n  json: {\n    ...clip,\n    clip_index: index + 1,\n    project_folder: projectFolder,\n    source_url: URL,\n    created_at: new Date().toISOString()\n  }\n}));"}, "name": "parse_viral_clips", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Get clip data\nCLIP_INDEX={{$json.clip_index}}\nSTART_TIME={{$json.start}}\nEND_TIME={{$json.end}}\nCLIP_TITLE='{{$json.title}}'\nPROJECT_FOLDER='{{$json.project_folder}}'\nSOURCE_URL='{{$json.source_url}}'\n\n# Sanitize title for filename\nSAFE_TITLE=$(echo \"$CLIP_TITLE\" | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g' | cut -c1-50)\n\n# Create clip filename with viral factory naming\nCLIP_FILENAME=\"clip_$(printf '%03d' $CLIP_INDEX)_${SAFE_TITLE}.mp4\"\nCLIP_PATH=\"/data/$PROJECT_FOLDER/mp4/$CLIP_FILENAME\"\n\necho \"Generating viral clip: $CLIP_FILENAME\"\necho \"Time range: ${START_TIME}s to ${END_TIME}s\"\necho \"Project: $PROJECT_FOLDER\"\n\n# Generate vertical (9:16) viral clip with NVENC encoding\nffmpeg -hide_banner -y \\\n  -ss \"$START_TIME\" -to \"$END_TIME\" \\\n  -i \"/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n  -vf \"scale=1080:1920:force_original_aspect_ratio=increase,crop=1080:1920,subtitles=/data/$PROJECT_FOLDER/srt/transcript.srt:force_style='FontName=Arial Bold,FontSize=32,PrimaryColour=&Hffffff,OutlineColour=&H000000,BorderStyle=3,Outline=2,Shadow=1,MarginV=100'\" \\\n  -c:v h264_nvenc -preset p6 -tune hq -b:v 8M -maxrate 10M -bufsize 16M \\\n  -c:a aac -b:a 192k -ar 44100 \\\n  -movflags +faststart \\\n  \"$CLIP_PATH\"\n\n# Verify clip was created\nif [ -f \"$CLIP_PATH\" ]; then\n  FILESIZE=$(stat -f%z \"$CLIP_PATH\" 2>/dev/null || stat -c%s \"$CLIP_PATH\" 2>/dev/null || echo \"0\")\n  echo \"✅ Viral clip generated: $CLIP_FILENAME (${FILESIZE} bytes)\"\nelse\n  echo \"❌ Failed to generate clip: $CLIP_FILENAME\"\n  exit 1\nfi"}, "name": "generate_viral_clip", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1400, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Collect all clip data for manifest\nPROJECT_FOLDER='{{$json.project_folder}}'\nSOURCE_URL='{{$json.source_url}}'\n\necho \"Creating viral factory manifest for: $PROJECT_FOLDER\"\n\n# Create manifest entry\nMANIFEST_ENTRY=$(cat << EOF\n{\n  \"clip\": \"mp4/clip_$(printf '%03d' {{$json.clip_index}})_$(echo '{{$json.title}}' | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g' | cut -c1-50).mp4\",\n  \"start\": {{$json.start}},\n  \"end\": {{$json.end}},\n  \"title\": \"{{$json.title}}\",\n  \"justification\": \"{{$json.justification}}\",\n  \"mode\": \"viral-vertical\",\n  \"resolution\": \"1080x1920\",\n  \"createdAt\": \"{{$json.created_at}}\",\n  \"sourceUrl\": \"$SOURCE_URL\",\n  \"projectFolder\": \"$PROJECT_FOLDER\"\n}\nEOF\n)\n\n# Append to manifest file\necho \"$MANIFEST_ENTRY\" >> \"/data/$PROJECT_FOLDER/manifest.jsonl\"\n\necho \"Manifest entry added for clip {{$json.clip_index}}\""}, "name": "update_manifest", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1600, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Get project info from first item\nPROJECT_FOLDER='{{$node[\"parse_viral_clips\"].first().json[\"project_folder\"]}}'\nSOURCE_URL='{{$node[\"webhook\"].json[\"body\"][\"url\"]}}'\n\n# Count generated clips\nCLIP_COUNT=$(find \"/data/$PROJECT_FOLDER/mp4\" -name \"*.mp4\" -type f | wc -l)\n\n# Get total size\nTOTAL_SIZE=$(du -sh \"/data/$PROJECT_FOLDER\" | cut -f1)\n\n# Create success response\necho \"{\n  \\\"status\\\": \\\"success\\\",\n  \\\"message\\\": \\\"Viral clip factory completed successfully\\\",\n  \\\"project_folder\\\": \\\"$PROJECT_FOLDER\\\",\n  \\\"clips_generated\\\": $CLIP_COUNT,\n  \\\"total_size\\\": \\\"$TOTAL_SIZE\\\",\n  \\\"source_url\\\": \\\"$SOURCE_URL\\\",\n  \\\"clips_location\\\": \\\"/data/$PROJECT_FOLDER/mp4/\\\",\n  \\\"manifest_file\\\": \\\"/data/$PROJECT_FOLDER/manifest.jsonl\\\",\n  \\\"ready_for_upload\\\": true\n}\"\n\necho \"🎬 Viral Clip Factory completed: $PROJECT_FOLDER\"\necho \"📁 Clips ready at: /data/$PROJECT_FOLDER/mp4/\"\necho \"📊 Generated $CLIP_COUNT viral clips\""}, "name": "viral_factory_response", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1800, 100]}], "connections": {"webhook": {"main": [[{"node": "setup_viral_factory", "type": "main", "index": 0}]]}, "setup_viral_factory": {"main": [[{"node": "download_video", "type": "main", "index": 0}]]}, "download_video": {"main": [[{"node": "transcribe_whisper", "type": "main", "index": 0}]]}, "transcribe_whisper": {"main": [[{"node": "analyze_viral_moments", "type": "main", "index": 0}]]}, "analyze_viral_moments": {"main": [[{"node": "parse_viral_clips", "type": "main", "index": 0}]]}, "parse_viral_clips": {"main": [[{"node": "generate_viral_clip", "type": "main", "index": 0}]]}, "generate_viral_clip": {"main": [[{"node": "update_manifest", "type": "main", "index": 0}]]}, "update_manifest": {"main": [[{"node": "viral_factory_response", "type": "main", "index": 0}]]}}, "settings": {}}