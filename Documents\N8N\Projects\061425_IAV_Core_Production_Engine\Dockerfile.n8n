FROM n8nio/n8n:latest

USER root

# ---- tools for video + python stack ----
RUN apk add --no-cache ffmpeg yt-dlp python3 py3-pip && \
    pip3 install --break-system-packages --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu && \
    pip3 install --break-system-packages --no-cache-dir openai-whisper

# ---- install postgres driver ----
RUN npm install -g pg

USER node
