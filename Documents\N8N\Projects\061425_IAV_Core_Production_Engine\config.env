# ===================================================================
# IAV CORE PRODUCTION ENGINE - VIRAL CLIP FACTORY CONFIGURATION
# ===================================================================

# ===== USER CREDENTIALS =====
# Your n8n login credentials (for reference)
N8N_USER_EMAIL=<EMAIL>
N8N_USER_PASSWORD=Indocloud#0283

# n8n API Key (32-character key from Settings → API Keys in n8n UI)
# IMPORTANT: Replace with actual 32-char key from n8n UI
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NjExODNjZi1hNjc4LTRmODEtOWIyZC1mODJmYzM2YjY5YTgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwMTE5ODQ5fQ.9n1-sMYCwrjXjTn-FAuuMl9tbe1qpmO4_JkCaXpsnwk

# Webhook authentication (optional - for securing webhook endpoints)
WEBHOOK_AUTH_TOKEN=your-webhook-token-here

# ===== DATABASE CONFIGURATION =====
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n
POSTGRES_DB=n8n

# ===== N8N CONFIGURATION =====
N8N_BASIC_AUTH_ACTIVE=false
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n
DB_POSTGRESDB_PASSWORD=n8n
N8N_REDIS_HOST=redis
GENERIC_TIMEZONE=UTC
N8N_HOST=0.0.0.0
N8N_PORT=8678
N8N_PROTOCOL=http
WEBHOOK_URL=http://localhost:8678
N8N_LOG_LEVEL=info
N8N_METRICS=true

# ===== AI MODEL CONFIGURATION =====
# Whisper ASR Model Configuration
ASR_MODEL=large-v3
ASR_ENGINE=openai_whisper
WHISPER_PORT=9100

# LLaMA Model Configuration
LLAMA_MODEL=llama3.1:8b-instruct-q4_K_M
OLLAMA_HOST=0.0.0.0
OLLAMA_PORT=11534
OLLAMA_ORIGINS=*
NVIDIA_VISIBLE_DEVICES=all

# ===== VIDEO PROCESSING CONFIGURATION =====
# FFmpeg encoding settings
VIDEO_CODEC=h264_nvenc
VIDEO_PRESET=p6
VIDEO_TUNE=hq
VIDEO_BITRATE=8M
VIDEO_MAXRATE=10M
VIDEO_BUFSIZE=16M
AUDIO_CODEC=aac
AUDIO_BITRATE=192k
AUDIO_SAMPLERATE=44100

# Output format settings
OUTPUT_WIDTH=1080
OUTPUT_HEIGHT=1920
OUTPUT_FPS=29.97

# Subtitle styling
SUBTITLE_FONT=Arial Bold
SUBTITLE_SIZE=32
SUBTITLE_COLOR=&Hffffff
SUBTITLE_OUTLINE_COLOR=&H000000
SUBTITLE_OUTLINE_WIDTH=2
SUBTITLE_MARGIN=100

# ===== FACTORY CONFIGURATION =====
# Maximum clips per video
MAX_CLIPS_PER_VIDEO=5

# Retry configuration
MAX_DOWNLOAD_RETRIES=3
MAX_TRANSCRIPTION_RETRIES=2
MAX_GENERATION_RETRIES=2

# Timeout settings (in seconds)
DOWNLOAD_TIMEOUT=1800
TRANSCRIPTION_TIMEOUT=3600
AI_ANALYSIS_TIMEOUT=600
CLIP_GENERATION_TIMEOUT=1200

# ===== REDIS CONFIGURATION =====
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_JOB_QUEUE=iav_job_queue
REDIS_STATUS_KEY=iav_status

# ===== FRONTEND CONFIGURATION =====
STREAMLIT_PORT=8601
FACTORY_UI_TITLE=IAV Viral Clip Factory
FACTORY_UI_DESCRIPTION=Transform YouTube videos into viral clips automatically

# ===== FILE SYSTEM CONFIGURATION =====
DATA_ROOT=/data
OUTPUT_ROOT=/data/factory_output
FAILED_JOBS_DIR=/data/factory_output/failed_jobs
LOGS_DIR_NAME=logs
CLIPS_DIR_NAME=mp4
SOURCE_DIR_NAME=src
TRANSCRIPT_DIR_NAME=srt

# ===== MONITORING & HEALTH =====
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=5
