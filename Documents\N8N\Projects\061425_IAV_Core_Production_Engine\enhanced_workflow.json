{"name": "IAV Viral Clip Factory - Enhanced with Real-time Status", "nodes": [{"parameters": {"rule": {"interval": [{"field": "seconds", "secondsInterval": 10}]}}, "name": "Redis Queue Monitor", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [200, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Check Redis for new jobs\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_QUEUE=\"${REDIS_JOB_QUEUE}\"\n\necho \"Checking Redis queue: $JOB_QUEUE\"\n\n# Pop job from Redis queue (blocking for 1 second)\nJOB_DATA=$(redis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" BRPOP \"$JOB_QUEUE\" 1 2>/dev/null || echo \"\")\n\nif [ -z \"$JOB_DATA\" ]; then\n  echo \"No jobs in queue\"\n  exit 0\nfi\n\n# Extract job data (Redis returns: queue_name job_data)\nJOB_JSON=$(echo \"$JOB_DATA\" | sed 's/^[^ ]* //')\necho \"Processing job: $JOB_JSON\"\necho \"$JOB_JSON\""}, "name": "Check Redis <PERSON>", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [400, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.stdout}}", "operation": "isNotEmpty"}, {"value1": "={{$json.stdout}}", "operation": "notContains", "value2": "No jobs in queue"}]}}, "name": "Job Available?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 100]}, {"parameters": {"code": "// Parse job data from Redis\nconst stdout = $input.first().json.stdout || '';\n\nif (!stdout || stdout.includes('No jobs in queue')) {\n  return [];\n}\n\ntry {\n  // Extract JSON from the output\n  const lines = stdout.split('\\n');\n  const jobLine = lines.find(line => line.trim().startsWith('{'));\n  \n  if (!jobLine) {\n    throw new Error('No valid JSON found in job data');\n  }\n  \n  const jobData = JSON.parse(jobLine.trim());\n  \n  // Validate required fields\n  if (!jobData.url) {\n    throw new Error('Job missing required URL field');\n  }\n  \n  // Add processing metadata\n  jobData.processing_started_at = new Date().toISOString();\n  jobData.job_id = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  \n  return [{ json: jobData }];\n  \n} catch (error) {\n  throw new Error(`Failed to parse job data: ${error.message}`);\n}"}, "name": "Parse Job Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with job start status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$json.job_id}}'\n\n# Set initial status\nSTATUS_JSON='{\"status\": \"Step 1/4: Initializing job...\", \"percent_complete\": 5, \"job_id\": \"'$JOB_ID'\", \"updated_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting initial status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 1800\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 1800\n\necho \"✅ Initial status set in Redis\""}, "name": "Set Initial Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [900, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Initialize job processing\nURL='{{$json.url}}'\nJOB_ID='{{$json.job_id}}'\nDATE=$(date +%Y-%m-%d)\nTIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')\n\necho \"=== STARTING JOB PROCESSING ===\"\necho \"Job ID: $JOB_ID\"\necho \"URL: $URL\"\necho \"Date: $DATE\"\necho \"Timestamp: $TIMESTAMP\"\n\n# Retry logic for video info extraction\nfor attempt in {1..3}; do\n  echo \"Attempt $attempt: Extracting video info...\"\n  \n  if VIDEO_INFO=$(yt-dlp --print \"%(title)s|%(id)s|%(duration)s\" \"$URL\" 2>/dev/null); then\n    echo \"Video info extracted successfully\"\n    break\n  else\n    echo \"Attempt $attempt failed\"\n    if [ $attempt -eq 3 ]; then\n      echo \"ERROR: Failed to extract video info after 3 attempts\"\n      exit 1\n    fi\n    sleep 5\n  fi\ndone\n\n# Parse video information\nVIDEO_TITLE=$(echo \"$VIDEO_INFO\" | cut -d'|' -f1 | sed 's/[^a-zA-Z0-9 ]/_/g' | sed 's/__*/_/g' | sed 's/^_\\|_$//g')\nVIDEO_ID=$(echo \"$VIDEO_INFO\" | cut -d'|' -f2)\nVIDEO_DURATION=$(echo \"$VIDEO_INFO\" | cut -d'|' -f3)\n\n# Validate video duration (max 3600 seconds = 1 hour)\nif [ \"$VIDEO_DURATION\" -gt 3600 ]; then\n  echo \"ERROR: Video too long ($VIDEO_DURATION seconds). Maximum allowed: 3600 seconds\"\n  exit 1\nfi\n\n# Create project folder with job ID\nPROJECT_FOLDER=\"${DATE}_${VIDEO_TITLE}_${JOB_ID}\"\necho \"Project folder: $PROJECT_FOLDER\"\n\n# Create directory structure\nmkdir -p \"/data/$PROJECT_FOLDER/src\"\nmkdir -p \"/data/$PROJECT_FOLDER/mp4\"\nmkdir -p \"/data/$PROJECT_FOLDER/srt\"\nmkdir -p \"/data/$PROJECT_FOLDER/logs\"\n\n# Create job metadata file\ncat > \"/data/$PROJECT_FOLDER/job_metadata.json\" << EOF\n{\n  \"job_id\": \"$JOB_ID\",\n  \"url\": \"$URL\",\n  \"video_id\": \"$VIDEO_ID\",\n  \"video_title\": \"$VIDEO_TITLE\",\n  \"video_duration\": $VIDEO_DURATION,\n  \"project_folder\": \"$PROJECT_FOLDER\",\n  \"started_at\": \"$(date -Iseconds)\",\n  \"status\": \"initialized\"\n}\nEOF\n\necho \"✅ Job initialization completed successfully\"\necho \"Project folder created: $PROJECT_FOLDER\""}, "name": "Initialize Job", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1100, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with download status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\n# Set download status\nSTATUS_JSON='{\"status\": \"Step 1/4: Downloading video...\", \"percent_complete\": 10, \"job_id\": \"'$JOB_ID'\", \"updated_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting download status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 1800\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 1800\n\necho \"✅ Download status set in Redis\""}, "name": "Set Download Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1200, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.exitCode}}", "value2": "0"}]}}, "name": "Init Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1300, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Get project info from previous step\nPROJECT_FOLDER=$(echo '{{$node[\"Initialize Job\"].json.stdout}}' | grep 'Project folder created:' | sed 's/.*: //')\nURL='{{$node[\"Parse Job Data\"].json.url}}'\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\necho \"=== DOWNLOADING VIDEO ===\"\necho \"Project: $PROJECT_FOLDER\"\necho \"URL: $URL\"\n\n# Update job status\necho '{\"status\": \"downloading\", \"step\": \"video_download\", \"updated_at\": \"'$(date -Iseconds)'\"}' > \"/data/$PROJECT_FOLDER/logs/status.json\"\n\n# Download with retry logic and timeout\nDOWNLOAD_SUCCESS=false\nfor attempt in {1..${MAX_DOWNLOAD_RETRIES}}; do\n  echo \"Download attempt $attempt/${MAX_DOWNLOAD_RETRIES}...\"\n  \n  if timeout ${DOWNLOAD_TIMEOUT} yt-dlp \\\n    -f 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best' \\\n    --merge-output-format mp4 \\\n    --sponsorblock-remove all \\\n    --no-playlist \\\n    --max-filesize 2G \\\n    -o \"/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n    \"$URL\" 2>&1 | tee \"/data/$PROJECT_FOLDER/logs/download.log\"; then\n    \n    # Verify file was created and has reasonable size\n    if [ -f \"/data/$PROJECT_FOLDER/src/source_video.mp4\" ]; then\n      FILE_SIZE=$(stat -f%z \"/data/$PROJECT_FOLDER/src/source_video.mp4\" 2>/dev/null || stat -c%s \"/data/$PROJECT_FOLDER/src/source_video.mp4\" 2>/dev/null)\n      if [ \"$FILE_SIZE\" -gt 1000000 ]; then  # At least 1MB\n        echo \"✅ Download successful (${FILE_SIZE} bytes)\"\n        DOWNLOAD_SUCCESS=true\n        break\n      else\n        echo \"❌ Downloaded file too small: ${FILE_SIZE} bytes\"\n      fi\n    else\n      echo \"❌ Download file not found\"\n    fi\n  else\n    echo \"❌ Download command failed\"\n  fi\n  \n  if [ $attempt -lt ${MAX_DOWNLOAD_RETRIES} ]; then\n    echo \"Waiting 10 seconds before retry...\"\n    sleep 10\n  fi\ndone\n\nif [ \"$DOWNLOAD_SUCCESS\" = false ]; then\n  echo \"ERROR: Video download failed after ${MAX_DOWNLOAD_RETRIES} attempts\"\n  echo '{\"status\": \"failed\", \"step\": \"video_download\", \"error\": \"Download failed after retries\", \"failed_at\": \"'$(date -Iseconds)'\"}' > \"/data/$PROJECT_FOLDER/logs/status.json\"\n  exit 1\nfi\n\necho \"✅ Video download completed successfully\""}, "name": "Download Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1500, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.exitCode}}", "value2": "0"}]}}, "name": "Download Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1700, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with transcription status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\n# Set transcription status\nSTATUS_JSON='{\"status\": \"Step 2/4: Transcribing audio with AI...\", \"percent_complete\": 30, \"job_id\": \"'$JOB_ID'\", \"updated_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting transcription status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 1800\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 1800\n\necho \"✅ Transcription status set in Redis\""}, "name": "Set Transcription Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1900, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Get project info\nPROJECT_FOLDER=$(echo '{{$node[\"Initialize Job\"].json.stdout}}' | grep 'Project folder created:' | sed 's/.*: //')\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\necho \"=== TRANSCRIBING AUDIO ===\"\necho \"Project: $PROJECT_FOLDER\"\necho \"Job ID: $JOB_ID\"\n\n# Transcribe with Whisper\necho \"Starting transcription...\"\nif curl -X POST \\\n  -F \"audio_file=@/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n  -F \"task=transcribe\" \\\n  -F \"language=en\" \\\n  -F \"output=srt\" \\\n  http://whisper:9000/asr \\\n  -o \"/data/$PROJECT_FOLDER/srt/transcript.srt\" \\\n  --max-time ${TRANSCRIPTION_TIMEOUT}; then\n  \n  # Verify transcript was created\n  if [ -f \"/data/$PROJECT_FOLDER/srt/transcript.srt\" ] && [ -s \"/data/$PROJECT_FOLDER/srt/transcript.srt\" ]; then\n    echo \"✅ Transcription completed successfully\"\n  else\n    echo \"ERROR: Transcript file is empty or missing\"\n    exit 1\n  fi\nelse\n  echo \"ERROR: Transcription failed\"\n  exit 1\nfi"}, "name": "Transcribe Audio", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2100, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.exitCode}}", "value2": "0"}]}}, "name": "Transcription Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2300, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with AI analysis status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\n# Set AI analysis status\nSTATUS_JSON='{\"status\": \"Step 3/4: AI analyzing for viral moments...\", \"percent_complete\": 50, \"job_id\": \"'$JOB_ID'\", \"updated_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting AI analysis status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 1800\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 1800\n\necho \"✅ AI analysis status set in Redis\""}, "name": "Set AI Analysis Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2500, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Get project info\nPROJECT_FOLDER=$(echo '{{$node[\"Initialize Job\"].json.stdout}}' | grep 'Project folder created:' | sed 's/.*: //')\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\necho \"=== AI VIRAL MOMENT ANALYSIS ===\"\necho \"Project: $PROJECT_FOLDER\"\necho \"Job ID: $JOB_ID\"\n\n# Read transcript for AI analysis\nTRANSCRIPT_TEXT=$(cat \"/data/$PROJECT_FOLDER/srt/transcript.srt\" | grep -v '^[0-9]*$' | grep -v '^[0-9][0-9]:[0-9][0-9]:[0-9][0-9]' | grep -v '^$' | tr '\\n' ' ')\n\necho \"Analyzing transcript with AI...\"\n\n# Call LLaMA for viral moment analysis\nAI_RESPONSE=$(curl -X POST http://ollama:11434/api/generate \\\n  -H \"Content-Type: application/json\" \\\n  -d \"{\n    \\\"model\\\": \\\"${LLAMA_MODEL}\\\",\n    \\\"prompt\\\": \\\"You are a world-class viral video producer. Analyze this transcript and find exactly 5 viral moments that will perform well on TikTok, Instagram Reels, and YouTube Shorts. Output ONLY a JSON array with objects containing: start_time (seconds), end_time (seconds), title (hook), justification. Focus on: emotional peaks, controversial takes, surprising moments, quotable lines, cliffhangers. Transcript: $TRANSCRIPT_TEXT\\\",\n    \\\"stream\\\": false,\n    \\\"options\\\": {\n      \\\"temperature\\\": 0.7,\n      \\\"max_tokens\\\": 2000\n    }\n  }\" \\\n  --max-time ${AI_ANALYSIS_TIMEOUT})\n\necho \"AI analysis completed\"\necho \"$AI_RESPONSE\" > \"/data/$PROJECT_FOLDER/logs/ai_response.json\""}, "name": "AI Viral Analysis", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2700, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.exitCode}}", "value2": "0"}]}}, "name": "AI Analysis Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2900, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with clip generation status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\n\n# Set clip generation status\nSTATUS_JSON='{\"status\": \"Step 4/4: Generating viral clips...\", \"percent_complete\": 75, \"job_id\": \"'$JOB_ID'\", \"updated_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting clip generation status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 1800\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 1800\n\necho \"✅ Clip generation status set in Redis\""}, "name": "Set Clip Generation Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3100, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Get project info\nPROJECT_FOLDER=$(echo '{{$node[\"Initialize Job\"].json.stdout}}' | grep 'Project folder created:' | sed 's/.*: //')\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\nURL='{{$node[\"Parse Job Data\"].json.url}}'\n\necho \"=== GENERATING VIRAL CLIPS ===\"\necho \"Project: $PROJECT_FOLDER\"\necho \"Job ID: $JOB_ID\"\n\n# Parse AI response and generate clips\nAI_RESPONSE=$(cat \"/data/$PROJECT_FOLDER/logs/ai_response.json\")\nCLIPS_JSON=$(echo \"$AI_RESPONSE\" | jq -r '.response' | grep -o '\\[.*\\]' | head -1)\n\necho \"Clips data: $CLIPS_JSON\"\necho \"$CLIPS_JSON\" > \"/data/$PROJECT_FOLDER/logs/clips_data.json\"\n\n# Generate clips using FFmpeg with NVENC\nCLIP_COUNT=0\necho \"$CLIPS_JSON\" | jq -r '.[] | @base64' | while IFS= read -r clip_data; do\n  CLIP_COUNT=$((CLIP_COUNT + 1))\n  CLIP_INFO=$(echo \"$clip_data\" | base64 -d)\n  \n  START_TIME=$(echo \"$CLIP_INFO\" | jq -r '.start_time // .start')\n  END_TIME=$(echo \"$CLIP_INFO\" | jq -r '.end_time // .end')\n  TITLE=$(echo \"$CLIP_INFO\" | jq -r '.title' | sed 's/[^a-zA-Z0-9 ]/_/g')\n  \n  CLIP_PATH=\"/data/$PROJECT_FOLDER/mp4/clip_$(printf '%03d' $CLIP_COUNT)_${TITLE}.mp4\"\n  \n  echo \"Generating clip $CLIP_COUNT: $START_TIME to $END_TIME\"\n  \n  ffmpeg -hide_banner -y \\\n    -ss \"$START_TIME\" -to \"$END_TIME\" \\\n    -i \"/data/$PROJECT_FOLDER/src/source_video.mp4\" \\\n    -vf \"scale=${OUTPUT_WIDTH}:${OUTPUT_HEIGHT}:force_original_aspect_ratio=increase,crop=${OUTPUT_WIDTH}:${OUTPUT_HEIGHT},subtitles=/data/$PROJECT_FOLDER/srt/transcript.srt:force_style='FontName=${SUBTITLE_FONT},FontSize=${SUBTITLE_SIZE},PrimaryColour=${SUBTITLE_COLOR},OutlineColour=${SUBTITLE_OUTLINE_COLOR},BorderStyle=3,Outline=${SUBTITLE_OUTLINE_WIDTH},Shadow=1,MarginV=${SUBTITLE_MARGIN}'\" \\\n    -c:v ${VIDEO_CODEC} -preset ${VIDEO_PRESET} -tune ${VIDEO_TUNE} -b:v ${VIDEO_BITRATE} -maxrate ${VIDEO_MAXRATE} -bufsize ${VIDEO_BUFSIZE} \\\n    -c:a ${AUDIO_CODEC} -b:a ${AUDIO_BITRATE} -ar ${AUDIO_SAMPLERATE} \\\n    -movflags +faststart \\\n    \"$CLIP_PATH\"\n  \n  # Add to manifest\n  echo \"{\\\"clip\\\":\\\"mp4/clip_$(printf '%03d' $CLIP_COUNT)_${TITLE}.mp4\\\",\\\"start\\\":$START_TIME,\\\"end\\\":$END_TIME,\\\"title\\\":\\\"$TITLE\\\",\\\"createdAt\\\":\\\"$(date -Iseconds)\\\",\\\"srcUrl\\\":\\\"$URL\\\"}\" >> \"/data/$PROJECT_FOLDER/manifest.jsonl\"\ndone\n\necho \"✅ All clips generated successfully\""}, "name": "Generate Clips", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3300, 100], "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.exitCode}}", "value2": "0"}]}}, "name": "Generation Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3500, 100]}, {"parameters": {"command": "=set -euo pipefail\n\n# Update Redis with completion status\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\nPROJECT_FOLDER=$(echo '{{$node[\"Initialize Job\"].json.stdout}}' | grep 'Project folder created:' | sed 's/.*: //')\n\n# Count generated clips\nCLIP_COUNT=$(find \"/data/$PROJECT_FOLDER/mp4\" -name \"*.mp4\" | wc -l)\n\n# Set completion status\nSTATUS_JSON='{\"status\": \"✅ Process complete! Generated '$CLIP_COUNT' viral clips\", \"percent_complete\": 100, \"job_id\": \"'$JOB_ID'\", \"clips_generated\": '$CLIP_COUNT', \"project_folder\": \"'$PROJECT_FOLDER'\", \"completed_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting completion status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 3600\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 3600\n\necho \"🎉 Job completed successfully! Generated $CLIP_COUNT clips\""}, "name": "Set Completion Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3700, 100], "continueOnFail": true}, {"parameters": {"command": "=set -euo pipefail\n\n# Handle job failure\nREDIS_HOST=\"${REDIS_HOST}\"\nREDIS_PORT=\"${REDIS_PORT}\"\nJOB_ID='{{$node[\"Parse Job Data\"].json.job_id}}'\nERROR_STEP=\"{{$node[\"Init Success?\"].json.error || $node[\"Download Success?\"].json.error || $node[\"Transcription Success?\"].json.error || $node[\"AI Analysis Success?\"].json.error || $node[\"Generation Success?\"].json.error || \"Unknown error\"}}'\n\n# Set error status\nSTATUS_JSON='{\"status\": \"❌ Error! Check failed jobs for details\", \"percent_complete\": 100, \"job_id\": \"'$JOB_ID'\", \"error\": \"'$ERROR_STEP'\", \"failed_at\": \"'$(date -Iseconds)'\"}'\n\necho \"Setting error status: $STATUS_JSON\"\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_current_job_status\" \"$STATUS_JSON\" EX 3600\nredis-cli -h \"$REDIS_HOST\" -p \"$REDIS_PORT\" SET \"iav_job_status:$JOB_ID\" \"$STATUS_JSON\" EX 3600\n\n# Call error handler\n/app/error_handler.sh handle_failure \"$PROJECT_FOLDER\" \"$ERROR_STEP\" \"Processing failed\" \"$JOB_ID\"\n\necho \"❌ Job failed and moved to failed jobs directory\""}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3700, 300], "continueOnFail": true}], "connections": {"Redis Queue Monitor": {"main": [[{"node": "Check Redis <PERSON>", "type": "main", "index": 0}]]}, "Check Redis Queue": {"main": [[{"node": "Job Available?", "type": "main", "index": 0}]]}, "Job Available?": {"main": [[{"node": "Parse Job Data", "type": "main", "index": 0}]]}, "Parse Job Data": {"main": [[{"node": "Set Initial Status", "type": "main", "index": 0}]]}, "Set Initial Status": {"main": [[{"node": "Initialize Job", "type": "main", "index": 0}]]}, "Initialize Job": {"main": [[{"node": "Init Success?", "type": "main", "index": 0}]]}, "Init Success?": {"main": [[{"node": "Set Download Status", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Download Status": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Download Success?", "type": "main", "index": 0}]]}, "Download Success?": {"main": [[{"node": "Set Transcription Status", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Transcription Status": {"main": [[{"node": "Transcribe Audio", "type": "main", "index": 0}]]}, "Transcribe Audio": {"main": [[{"node": "Transcription Success?", "type": "main", "index": 0}]]}, "Transcription Success?": {"main": [[{"node": "Set AI Analysis Status", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set AI Analysis Status": {"main": [[{"node": "AI Viral Analysis", "type": "main", "index": 0}]]}, "AI Viral Analysis": {"main": [[{"node": "AI Analysis Success?", "type": "main", "index": 0}]]}, "AI Analysis Success?": {"main": [[{"node": "Set Clip Generation Status", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Clip Generation Status": {"main": [[{"node": "Generate Clips", "type": "main", "index": 0}]]}, "Generate Clips": {"main": [[{"node": "Generation Success?", "type": "main", "index": 0}]]}, "Generation Success?": {"main": [[{"node": "Set Completion Status", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}