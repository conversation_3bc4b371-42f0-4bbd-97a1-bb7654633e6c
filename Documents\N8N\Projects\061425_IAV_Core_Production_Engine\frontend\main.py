#!/usr/bin/env python3
"""
IAV Viral Clip Factory - Web Interface
A Streamlit application for submitting YouTube videos and monitoring clip generation.
"""

import os
import re
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional

import streamlit as st
import redis
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configuration
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_JOB_QUEUE = os.getenv("REDIS_JOB_QUEUE", "iav_job_queue")
REDIS_STATUS_KEY = os.getenv("REDIS_STATUS_KEY", "iav_status")
DATA_ROOT = os.getenv("DATA_ROOT", "/data")
OUTPUT_ROOT = os.getenv("OUTPUT_ROOT", "/data/factory_output")
FAILED_JOBS_DIR = os.getenv("FAILED_JOBS_DIR", "/data/factory_output/failed_jobs")

# Page configuration
st.set_page_config(
    page_title="IAV Viral Clip Factory",
    page_icon="🎬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ff6b6b;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)


class FactoryManager:
    """Manages Redis connections and factory operations."""
    
    def __init__(self):
        self.redis_client = None
        self.connect_redis()
    
    def connect_redis(self):
        """Connect to Redis with retry logic."""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # Test connection
            self.redis_client.ping()
        except Exception as e:
            st.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def add_job(self, youtube_url: str) -> bool:
        """Add a job to the processing queue."""
        if not self.redis_client:
            return False
        
        try:
            job_data = {
                "url": youtube_url,
                "submitted_at": datetime.now().isoformat(),
                "status": "queued"
            }
            self.redis_client.lpush(REDIS_JOB_QUEUE, json.dumps(job_data))
            return True
        except Exception as e:
            st.error(f"Failed to add job: {e}")
            return False
    
    def get_queue_status(self) -> Dict:
        """Get current queue status."""
        if not self.redis_client:
            return {"queue_length": 0, "jobs": []}
        
        try:
            queue_length = self.redis_client.llen(REDIS_JOB_QUEUE)
            jobs = []
            
            # Get up to 10 jobs from queue for display
            raw_jobs = self.redis_client.lrange(REDIS_JOB_QUEUE, 0, 9)
            for job_str in raw_jobs:
                try:
                    job = json.loads(job_str)
                    jobs.append(job)
                except json.JSONDecodeError:
                    continue
            
            return {"queue_length": queue_length, "jobs": jobs}
        except Exception as e:
            st.error(f"Failed to get queue status: {e}")
            return {"queue_length": 0, "jobs": []}


def validate_youtube_url(url: str) -> bool:
    """Validate YouTube URL format."""
    youtube_patterns = [
        r'https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+',
        r'https?://youtu\.be/[\w-]+',
        r'https?://(?:www\.)?youtube\.com/embed/[\w-]+',
        r'https?://(?:www\.)?youtube\.com/v/[\w-]+',
    ]

    return any(re.match(pattern, url) for pattern in youtube_patterns)


def monitor_job_progress(factory_manager: FactoryManager):
    """Monitor job progress with real-time status updates."""
    if not factory_manager.redis_client:
        st.error("Cannot monitor progress: Redis connection failed")
        return

    # Create containers for dynamic updates
    status_container = st.container()
    progress_container = st.container()

    with status_container:
        status_placeholder = st.empty()
        progress_placeholder = st.empty()

    # Monitoring loop
    max_wait_time = 20 * 60  # 20 minutes timeout
    check_interval = 2  # Check every 2 seconds
    start_time = time.time()

    last_status = "Initializing..."
    last_percent = 0

    with st.spinner("🚀 Starting video processing..."):
        while time.time() - start_time < max_wait_time:
            try:
                # Get current status from Redis
                status_data = factory_manager.redis_client.get("iav_current_job_status")

                if status_data:
                    try:
                        status_info = json.loads(status_data)
                        current_status = status_info.get("status", "Processing...")
                        current_percent = status_info.get("percent_complete", 0)

                        # Update display if status changed
                        if current_status != last_status or current_percent != last_percent:
                            with status_placeholder:
                                st.info(f"📊 **Status:** {current_status}")

                            with progress_placeholder:
                                st.progress(current_percent / 100.0)
                                st.write(f"Progress: {current_percent}%")

                            last_status = current_status
                            last_percent = current_percent

                        # Check for completion or error
                        if current_percent >= 100:
                            if "complete" in current_status.lower():
                                with status_placeholder:
                                    st.success(f"🎉 {current_status}")

                                clips_generated = status_info.get("clips_generated", 0)
                                project_folder = status_info.get("project_folder", "")

                                if clips_generated > 0:
                                    st.success(f"✅ Successfully generated {clips_generated} viral clips!")
                                    if project_folder:
                                        st.info(f"📁 **Clips location:** `data/{project_folder}/mp4/`")

                                    # Show download instructions
                                    st.markdown("""
                                    ### 🎬 Your Viral Clips Are Ready!

                                    **Next Steps:**
                                    1. 📁 Navigate to the clips folder shown above
                                    2. 📱 Upload clips directly to TikTok, Instagram Reels, or YouTube Shorts
                                    3. 🚀 Watch your content go viral!

                                    **Clip Specifications:**
                                    - ✅ Perfect 9:16 aspect ratio (1080x1920)
                                    - ✅ Embedded subtitles for accessibility
                                    - ✅ Optimized for mobile viewing
                                    - ✅ 30-90 seconds duration (ideal for engagement)
                                    """)

                                break
                            elif "error" in current_status.lower():
                                with status_placeholder:
                                    st.error(f"❌ {current_status}")

                                st.error("""
                                **Processing Failed**

                                Please check:
                                - YouTube URL is accessible
                                - Video is not too long (max 1 hour)
                                - System has sufficient resources

                                Check the 'Failed Jobs' tab for detailed error information.
                                """)
                                break

                    except json.JSONDecodeError:
                        # Status data is not valid JSON, continue monitoring
                        pass

                # Wait before next check
                time.sleep(check_interval)

            except Exception as e:
                st.error(f"Error monitoring progress: {e}")
                break

        else:
            # Timeout reached
            with status_placeholder:
                st.warning("⏰ Processing is taking longer than expected...")

            st.warning("""
            **Processing Timeout**

            The job is still running but taking longer than expected.
            You can:
            - Check the 'Queue Status' tab for updates
            - Wait for completion (processing continues in background)
            - Check system resources if this happens frequently
            """)

    # Force refresh to show updated data
    time.sleep(2)
    st.rerun()


def get_completed_jobs() -> List[Dict]:
    """Get list of completed jobs from filesystem."""
    completed_jobs = []
    data_path = Path(DATA_ROOT)
    
    if not data_path.exists():
        return completed_jobs
    
    # Look for date-based directories
    for item in data_path.iterdir():
        if item.is_dir() and re.match(r'\d{4}-\d{2}-\d{2}_', item.name):
            manifest_file = item / "manifest.jsonl"
            if manifest_file.exists():
                try:
                    clips = []
                    with open(manifest_file, 'r') as f:
                        for line in f:
                            if line.strip():
                                clips.append(json.loads(line.strip()))
                    
                    completed_jobs.append({
                        "project_name": item.name,
                        "clips_count": len(clips),
                        "created_at": item.stat().st_mtime,
                        "path": str(item),
                        "clips": clips
                    })
                except Exception as e:
                    st.warning(f"Error reading manifest for {item.name}: {e}")
    
    return sorted(completed_jobs, key=lambda x: x["created_at"], reverse=True)


def get_failed_jobs() -> List[Dict]:
    """Get list of failed jobs."""
    failed_jobs = []
    failed_path = Path(FAILED_JOBS_DIR)

    if not failed_path.exists():
        return failed_jobs

    for item in failed_path.iterdir():
        if item.is_dir():
            error_log = item / "logs" / "error.log"
            error_message = "Unknown error"

            if error_log.exists():
                try:
                    with open(error_log, 'r') as f:
                        error_message = f.read().strip()
                except Exception:
                    pass

            failed_jobs.append({
                "project_name": item.name,
                "failed_at": item.stat().st_mtime,
                "error_message": error_message,
                "path": str(item)
            })

    return sorted(failed_jobs, key=lambda x: x["failed_at"], reverse=True)


def get_current_job_status(factory_manager: FactoryManager) -> Optional[Dict]:
    """Get current job processing status from Redis."""
    if not factory_manager.redis_client:
        return None

    try:
        status_data = factory_manager.redis_client.get("iav_current_job_status")
        if status_data:
            return json.loads(status_data)
    except Exception:
        pass

    return None


def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🎬 IAV Viral Clip Factory</h1>', unsafe_allow_html=True)
    st.markdown("**Transform YouTube videos into viral clips automatically**")
    
    # Initialize factory manager
    factory = FactoryManager()
    
    # Sidebar
    with st.sidebar:
        st.header("🎛️ Factory Control")
        
        # Job submission
        st.subheader("Submit New Job")
        youtube_url = st.text_input(
            "YouTube URL",
            placeholder="https://youtube.com/watch?v=...",
            help="Enter a valid YouTube video URL"
        )
        
        if st.button("🚀 Generate Clips", type="primary"):
            if not youtube_url:
                st.error("Please enter a YouTube URL")
            elif not validate_youtube_url(youtube_url):
                st.error("Please enter a valid YouTube URL")
            else:
                if factory.add_job(youtube_url):
                    st.success("Job added to queue successfully!")
                    st.balloons()

                    # Start real-time status monitoring
                    monitor_job_progress(factory)
                else:
                    st.error("Failed to add job to queue")
        
        st.divider()

        # Live status indicator
        st.subheader("🔄 Live Status")
        current_status = get_current_job_status(factory)
        if current_status:
            status_text = current_status.get("status", "Processing...")
            progress = current_status.get("percent_complete", 0)

            if progress < 100:
                st.info(f"🔄 **Active:** {status_text}")
                st.progress(progress / 100.0)
            elif "complete" in status_text.lower():
                st.success("✅ **Job Complete!**")
            elif "error" in status_text.lower():
                st.error("❌ **Job Failed**")
        else:
            st.info("💤 **Idle:** No active jobs")

        st.divider()

        # Auto-refresh toggle
        auto_refresh = st.checkbox("Auto-refresh (30s)", value=True)
        if auto_refresh:
            time.sleep(30)
            st.rerun()

        # Manual refresh
        if st.button("🔄 Refresh Now"):
            st.rerun()
    
    # Main content area
    col1, col2, col3 = st.columns(3)
    
    # Get data
    queue_status = factory.get_queue_status()
    completed_jobs = get_completed_jobs()
    failed_jobs = get_failed_jobs()
    
    # Metrics
    with col1:
        st.metric(
            label="📋 Jobs in Queue",
            value=queue_status["queue_length"],
            help="Number of videos waiting to be processed"
        )
    
    with col2:
        st.metric(
            label="✅ Completed Jobs",
            value=len(completed_jobs),
            help="Number of successfully processed videos"
        )
    
    with col3:
        st.metric(
            label="❌ Failed Jobs",
            value=len(failed_jobs),
            help="Number of failed processing attempts"
        )
    
    # Current job status display
    current_status = get_current_job_status(factory)
    if current_status:
        with st.container():
            st.markdown("### 🔄 Current Processing Status")

            status_info = current_status
            status_text = status_info.get("status", "Processing...")
            progress_percent = status_info.get("percent_complete", 0)
            job_id = status_info.get("job_id", "Unknown")

            # Create columns for status display
            status_col1, status_col2 = st.columns([3, 1])

            with status_col1:
                st.info(f"**Job ID:** {job_id}")
                st.info(f"**Status:** {status_text}")
                st.progress(progress_percent / 100.0)
                st.write(f"Progress: {progress_percent}%")

            with status_col2:
                if progress_percent < 100:
                    st.markdown("🔄 **Processing...**")
                elif "complete" in status_text.lower():
                    st.markdown("✅ **Completed!**")
                elif "error" in status_text.lower():
                    st.markdown("❌ **Failed**")

            st.divider()

    # Tabs for different views
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Queue Status", "✅ Completed Jobs", "❌ Failed Jobs", "📊 Analytics"])
    
    with tab1:
        st.header("Current Queue")
        
        if queue_status["queue_length"] == 0:
            st.info("No jobs in queue. Submit a YouTube URL to get started!")
        else:
            st.write(f"**{queue_status['queue_length']} jobs** waiting to be processed")
            
            if queue_status["jobs"]:
                queue_df = pd.DataFrame(queue_status["jobs"])
                queue_df["submitted_at"] = pd.to_datetime(queue_df["submitted_at"])
                
                st.dataframe(
                    queue_df[["url", "submitted_at", "status"]],
                    use_container_width=True,
                    hide_index=True
                )
    
    with tab2:
        st.header("Completed Jobs")
        
        if not completed_jobs:
            st.info("No completed jobs yet. Submit a video to see results here!")
        else:
            for job in completed_jobs[:10]:  # Show last 10 jobs
                with st.expander(f"🎬 {job['project_name']} ({job['clips_count']} clips)"):
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        st.write(f"**Project:** {job['project_name']}")
                        st.write(f"**Clips Generated:** {job['clips_count']}")
                        st.write(f"**Created:** {datetime.fromtimestamp(job['created_at']).strftime('%Y-%m-%d %H:%M:%S')}")
                        st.write(f"**Path:** `{job['path']}`")
                    
                    with col2:
                        if st.button(f"📁 Open Folder", key=f"open_{job['project_name']}"):
                            st.info(f"Clips location: {job['path']}/mp4/")
                    
                    # Show clips details
                    if job["clips"]:
                        clips_df = pd.DataFrame(job["clips"])
                        if "title" in clips_df.columns:
                            st.dataframe(
                                clips_df[["title", "start", "end"]].head(),
                                use_container_width=True,
                                hide_index=True
                            )
    
    with tab3:
        st.header("Failed Jobs")
        
        if not failed_jobs:
            st.success("No failed jobs! 🎉")
        else:
            for job in failed_jobs[:5]:  # Show last 5 failed jobs
                with st.expander(f"❌ {job['project_name']}"):
                    st.write(f"**Project:** {job['project_name']}")
                    st.write(f"**Failed:** {datetime.fromtimestamp(job['failed_at']).strftime('%Y-%m-%d %H:%M:%S')}")
                    st.write(f"**Error:** {job['error_message']}")
                    st.write(f"**Path:** `{job['path']}`")
    
    with tab4:
        st.header("Factory Analytics")
        
        if completed_jobs:
            # Success rate chart
            total_attempts = len(completed_jobs) + len(failed_jobs)
            success_rate = len(completed_jobs) / total_attempts * 100 if total_attempts > 0 else 0
            
            col1, col2 = st.columns(2)
            
            with col1:
                fig = go.Figure(data=[
                    go.Bar(name='Completed', x=['Jobs'], y=[len(completed_jobs)], marker_color='green'),
                    go.Bar(name='Failed', x=['Jobs'], y=[len(failed_jobs)], marker_color='red')
                ])
                fig.update_layout(title='Job Success Rate', barmode='stack')
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Clips per job distribution
                clips_counts = [job["clips_count"] for job in completed_jobs]
                if clips_counts:
                    fig = px.histogram(
                        x=clips_counts,
                        title="Clips per Job Distribution",
                        labels={"x": "Number of Clips", "y": "Frequency"}
                    )
                    st.plotly_chart(fig, use_container_width=True)
            
            # Timeline
            if len(completed_jobs) > 1:
                timeline_data = []
                for job in completed_jobs:
                    timeline_data.append({
                        "date": datetime.fromtimestamp(job["created_at"]).date(),
                        "clips": job["clips_count"]
                    })
                
                timeline_df = pd.DataFrame(timeline_data)
                timeline_df = timeline_df.groupby("date")["clips"].sum().reset_index()
                
                fig = px.line(
                    timeline_df,
                    x="date",
                    y="clips",
                    title="Clips Generated Over Time",
                    labels={"clips": "Total Clips", "date": "Date"}
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No data available for analytics yet.")
    
    # Footer
    st.divider()
    st.markdown("**IAV Viral Clip Factory** - Powered by n8n, Ollama, and Streamlit")


if __name__ == "__main__":
    main()
