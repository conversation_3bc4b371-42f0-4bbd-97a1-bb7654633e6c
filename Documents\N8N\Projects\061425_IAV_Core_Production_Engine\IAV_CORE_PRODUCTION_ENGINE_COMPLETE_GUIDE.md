# IAV Core Production Engine - Complete Guide

## 🎯 What This Tool Does

The **IAV Core Production Engine** is an automated video processing system that transforms long-form YouTube videos into viral-ready short clips. It takes a YouTube URL as input and outputs 5 professionally formatted vertical videos (1080x1920) with embedded subtitles, ready for TikTok, Instagram Reels, and YouTube Shorts.

### Key Features
- **Automated Download**: Fetches videos from YouTube using yt-dlp
- **AI Transcription**: Uses local Whisper for accurate speech-to-text
- **Smart Clip Selection**: LLaMA 3.1 AI analyzes transcripts to find viral moments
- **Intelligent Framing**: Automatically detects and applies optimal aspect ratios
- **Professional Encoding**: NVENC GPU acceleration for fast, high-quality output
- **Organized Storage**: Date-based directory structure with searchable metadata
- **Zero Manual Work**: Single webhook call processes entire pipeline

## 🏗️ System Architecture

### Core Components
1. **n8n Workflow Engine**: Orchestrates the entire pipeline
2. **Ollama**: Runs LLaMA 3.1 8B and Whisper models locally
3. **PostgreSQL**: Stores workflow data and execution history
4. **Redis**: Handles caching and queue management
5. **Custom Docker Containers**: Pre-configured with FFmpeg, yt-dlp, and tools

### Processing Pipeline
```
YouTube URL → Download → Transcribe → AI Analysis → Clip Generation → Storage
     ↓            ↓          ↓           ↓             ↓            ↓
  yt-dlp      Whisper    LLaMA 3.1    FFmpeg      Organized     Manifest
             (local)    (8B model)   (NVENC)      Folders       Tracking
```

## 🚀 Installation & Setup

### Prerequisites
- **Windows 10/11** with WSL2 or **Linux**
- **Docker Desktop** with Docker Compose
- **NVIDIA GPU** with CUDA support (8GB+ VRAM recommended)
- **16GB+ RAM** (32GB recommended for optimal performance)
- **50GB+ free disk space** for models and video storage

### Step 1: Create Project Structure
```bash
# Create project directory
mkdir IAV_Core_Production_Engine
cd IAV_Core_Production_Engine

# Create required directories
mkdir -p data scripts models n8n_data db_data redis_data ollama_data
```

### Step 2: Create Configuration Files

**Create config.env:**
```bash
# n8n Configuration
N8N_USER_EMAIL=<EMAIL>
N8N_USER_PASSWORD=your-secure-password

# n8n API Key (get from UI after first setup)
N8N_API_KEY=your-32-character-api-key-here

# Webhook authentication (optional)
WEBHOOK_AUTH_TOKEN=your-webhook-token-here
```

### Step 3: Create Deployment Script

**Create deploy.sh:**
```bash
#!/bin/bash
set -e

echo "🚀 Starting IAV Core Production Engine Deployment..."

# Function to check service health
check_service_health() {
    local service_name=$1
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for $service_name to be healthy..."

    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps $service_name | grep -q "healthy"; then
            echo "✅ $service_name is healthy"
            return 0
        fi

        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 10
        attempt=$((attempt + 1))
    done

    echo "❌ $service_name failed to become healthy"
    return 1
}

# Function to pull Ollama models
pull_ollama_models() {
    echo "📥 Pulling Ollama models..."

    echo "   Pulling whisper-large-v3..."
    docker exec iav_ollama ollama pull whisper-large-v3

    echo "   Pulling llama3.1:8b-instruct-q4_K_M..."
    docker exec iav_ollama ollama pull llama3.1:8b-instruct-q4_K_M

    echo "✅ All models pulled successfully"
}

# Main deployment steps
echo "🔧 Building and starting services..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

echo "🏥 Checking service health..."
check_service_health postgres
check_service_health redis
check_service_health ollama
check_service_health n8n

# Wait for Ollama API
echo "⏳ Waiting for Ollama API..."
sleep 30

pull_ollama_models

echo ""
echo "🎉 IAV Core Production Engine deployed successfully!"
echo ""
echo "📊 Service Status:"
docker-compose ps
echo ""
echo "🌐 Access URLs:"
echo "   n8n Interface: http://localhost:5678"
echo "   Ollama API: http://localhost:11434"
echo ""
echo "📋 Next Steps:"
echo "   1. Open http://localhost:5678"
echo "   2. Create account with credentials from config.env"
echo "   3. Get API key from Settings → API Keys"
echo "   4. Update config.env with the API key"
echo "   5. Import workflow.json via UI"
echo ""
```

**Make it executable:**
```bash
chmod +x deploy.sh
./deploy.sh
```

### Step 4: Get API Key
1. Open http://localhost:5678
2. Create account with credentials from config.env
3. Go to Settings → API Keys → Create key
4. Update config.env with the 32-character API key

## 📁 Complete File Structure

```
IAV_Core_Production_Engine/
├── docker-compose.yaml          # Container orchestration
├── Dockerfile.n8n              # Custom n8n image with tools
├── config.env                  # Configuration and credentials
├── deploy.sh                   # Automated deployment script
├── workflow.json               # Complete n8n workflow definition
├── quick-recovery.ps1          # Windows recovery script
├── data/                       # Output directory (auto-created)
│   └── YYYY-MM-DD/            # Date-based organization
│       └── PROJECT_NAME/       # Project folders
│           ├── src/           # Source videos
│           ├── srt/           # Transcripts
│           ├── mp4/           # Final clips ⭐
│           └── manifest.jsonl # Metadata tracking
├── scripts/                   # Utility scripts
│   ├── findclip               # Search clips
│   ├── backup-clips.sh        # Backup management
│   ├── housekeeping.sh        # Cleanup automation
│   └── test-webhook.sh        # Testing utilities
├── n8n_data/                  # n8n persistent data
├── db_data/                   # PostgreSQL data
├── redis_data/                # Redis data
└── ollama_data/               # AI model storage
```

### Essential Utility Scripts

**Create scripts/test-webhook.sh:**
```bash
#!/bin/bash
# Test the IAV webhook with Rick Roll URL
echo "🧪 Testing IAV Core Production Engine..."

curl -X POST "http://localhost:5678/webhook/iav" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=dQw4w9WgXcQ"}' \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"
```

**Create scripts/findclip:**
```bash
#!/bin/bash
# Search clips by keyword, date, or metadata
SEARCH_TERM="$1"
DATA_DIR="./data"

if [ -z "$SEARCH_TERM" ]; then
    echo "Usage: ./findclip <search_term>"
    echo "       ./findclip -d YYYY-MM-DD  # Search by date"
    echo "       ./findclip -s             # Show statistics"
    exit 1
fi

if [ "$1" = "-s" ]; then
    echo "📊 Clip Statistics:"
    find "$DATA_DIR" -name "*.mp4" | wc -l | xargs echo "Total clips:"
    find "$DATA_DIR" -name "manifest.jsonl" -exec wc -l {} + | tail -1 | awk '{print "Total entries: " $1}'
    exit 0
fi

if [ "$1" = "-d" ]; then
    DATE="$2"
    echo "📅 Clips for $DATE:"
    find "$DATA_DIR/$DATE" -name "*.mp4" 2>/dev/null | sort
    exit 0
fi

echo "🔍 Searching for: $SEARCH_TERM"
find "$DATA_DIR" -name "manifest.jsonl" -exec grep -l "$SEARCH_TERM" {} \; | while read manifest; do
    echo "Found in: $(dirname "$manifest")"
    grep "$SEARCH_TERM" "$manifest"
done
```

## ⚙️ Core Configuration Files

### docker-compose.yaml
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: iav_postgres
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n_password
    volumes:
      - ./db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: iav_redis
    command: redis-server --appendonly yes
    volumes:
      - ./redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    container_name: iav_ollama
    ports:
      - "11434:11434"
    volumes:
      - ./data:/data
      - ./ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
      - NVIDIA_VISIBLE_DEVICES=all
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  n8n:
    build:
      context: .
      dockerfile: Dockerfile.n8n
    container_name: iav_n8n
    ports:
      - "5678:5678"
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password
      - QUEUE_BULL_REDIS_HOST=redis
      - QUEUE_BULL_REDIS_PORT=6379
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678
      - GENERIC_TIMEZONE=UTC
      - N8N_LOG_LEVEL=info
      - N8N_METRICS=true
    volumes:
      - ./data:/data
      - ./n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      ollama:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  n8n_data:
```

### Dockerfile.n8n
```dockerfile
FROM n8nio/n8n:latest

# Switch to root to install packages
USER root

# Update package lists and install dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    python3 \
    python3-pip \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# Install yt-dlp
RUN pip3 install --no-cache-dir yt-dlp

# Install FFmpeg with NVENC support
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Create aliases for yt-dlp and ffmpeg to be accessible in n8n
RUN ln -sf /usr/local/bin/yt-dlp /usr/bin/yt-dlp
RUN ln -sf /usr/bin/ffmpeg /usr/local/bin/ffmpeg

# Ensure the data directory exists and has proper permissions
RUN mkdir -p /data && chown -R node:node /data

# Switch back to node user
USER node

# Set working directory
WORKDIR /home/<USER>

# Expose n8n port
EXPOSE 5678

# Start n8n
CMD ["n8n"]
```

### workflow.json
Complete n8n workflow with 11 nodes:
1. **Webhook**: Receives POST requests with YouTube URLs
2. **Download**: yt-dlp fetches video in best quality
3. **Get Dimensions**: FFprobe analyzes video resolution
4. **Pick Mode**: Smart aspect ratio detection
5. **Whisper**: Local transcription to SRT format
6. **Read SRT**: Loads transcript for AI analysis
7. **LLaMA**: Selects 5 viral moments from transcript
8. **Parse JSON**: Processes AI response into clip data
9. **Encode**: FFmpeg creates clips with subtitles
10. **Manifest**: Generates searchable metadata
11. **Response**: Returns success JSON with clip count

## 🎮 How to Operate

### Basic Usage
```bash
# Send YouTube URL to webhook
curl -X POST "http://localhost:5678/webhook/iav" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=VIDEO_ID"}'

# Expected response:
{
  "status": "ok",
  "clips": 5,
  "mode": "center-crop",
  "message": "Successfully processed 5 clips using center-crop framing"
}
```

### Processing Time
- **Short videos (5-15 min)**: 3-8 minutes
- **Medium videos (15-45 min)**: 8-20 minutes  
- **Long videos (45+ min)**: 20-45 minutes

### Output Location
Clips are automatically saved to:
```
/data/YYYY-MM-DD/IAV_Core_Production_Engine/mp4/
├── clip_1_title.mp4
├── clip_2_title.mp4
├── clip_3_title.mp4
├── clip_4_title.mp4
└── clip_5_title.mp4
```

### Framing Modes
The system automatically detects optimal framing:
- **Native**: For vertical videos (≤0.6 aspect ratio)
- **Center-crop**: For landscape videos (crops to 9:16)
- **Split-stack**: Manual override with `?frame=stack` parameter

### Advanced Usage
```bash
# Force split-stack mode
curl -X POST "http://localhost:5678/webhook/iav?frame=stack" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=VIDEO_ID"}'

# Monitor processing
docker logs -f 061425_iav_core_production_engine-n8n-1

# Search clips
./findclip "keyword"
./findclip -d 2025-06-16  # Specific date
./findclip -s             # Statistics
```

## 🔧 Technical Specifications

### AI Models
- **LLaMA 3.1 8B Instruct (q4_K_M)**: Optimized for 8GB VRAM
- **Whisper Medium.en**: CPU-based transcription
- **Model Storage**: ~6GB total disk space

### Video Processing
- **Input**: Any YouTube-supported format
- **Output**: MP4, 1080x1920, 29.97+ fps
- **Encoding**: H.264 with NVENC acceleration
- **Audio**: AAC, 192kbps
- **Subtitles**: Embedded with custom styling

### Performance Targets
- **GPU Usage**: NVENC ≤45% during encoding
- **CPU Usage**: Whisper ~250% (multi-core)
- **VRAM Usage**: LLaMA ~3.8GB
- **Clip Duration**: 30-90 seconds each
- **Success Rate**: 95%+ with proper setup

## 🛠️ Troubleshooting

### Common Issues

**1. Webhook Not Registered**
```bash
# Restart n8n container
docker-compose restart n8n
# Wait 30 seconds, then test
```

**2. API Key Expired**
```bash
# Get new key from http://localhost:5678
# Update config.env
# No restart needed
```

**3. GPU Not Detected**
```bash
# Check NVIDIA drivers
nvidia-smi
# Verify Docker GPU support
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

**4. Out of Memory**
```bash
# Check available resources
docker stats
# Reduce video length or increase swap
```

**5. Download Failures**
```bash
# Update yt-dlp
docker exec container_name pip install -U yt-dlp
# Check for geo-restrictions
```

### Validation Commands
```bash
# System health check
docker-compose ps
curl http://localhost:5678/healthz

# Test webhook registration
curl http://localhost:5678/webhook-test/iav

# Verify GPU encoding
nvidia-smi dmon -s u -c 1

# Check clip output
ffprobe /data/YYYY-MM-DD/PROJECT/mp4/clip_1_*.mp4
```

## 📊 Monitoring & Maintenance

### Daily Operations
1. **Check system status**: `docker-compose ps`
2. **Process videos**: Send URLs to webhook
3. **Review clips**: Navigate to `/data/YYYY-MM-DD/PROJECT/mp4/`
4. **Upload to platforms**: Drag clips to TikTok Studio/CapCut

### Weekly Maintenance
```bash
# Generate reports
./daily-index.sh > weekly_report.csv

# Clean old files
./housekeeping.sh --dry-run  # Preview
./housekeeping.sh            # Execute

# Backup clips
./backup-clips.sh local      # USB backup
./backup-clips.sh cloud      # Cloud backup
```

### Performance Monitoring
```bash
# Resource usage
docker stats --no-stream

# Processing logs
docker logs --tail 50 061425_iav_core_production_engine-n8n-1

# Clip statistics
./findclip -s
```

## 🔒 Security & Best Practices

### API Security
- Use 32-character API keys (not JWT tokens)
- Rotate keys monthly
- Restrict webhook access to trusted networks
- Monitor execution logs for anomalies

### Data Management
- Implement automated backups
- Clean source files after 30 days
- Archive old clips after 90 days
- Monitor disk usage regularly

### Resource Management
- Limit concurrent executions to 1-2
- Monitor GPU temperature and usage
- Set up disk space alerts
- Use SSD storage for better performance

## 📈 Scaling & Optimization

### Single Machine Optimization
- **CPU**: 16+ cores for faster transcription
- **GPU**: RTX 4080/4090 for optimal NVENC performance
- **RAM**: 32GB+ for large video processing
- **Storage**: NVMe SSD for faster I/O

### Multi-Machine Scaling
- Separate Ollama to dedicated GPU server
- Use shared storage (NFS/SMB) for clips
- Load balance webhook requests
- Implement queue management for high volume

## 🎯 Success Metrics

### Quality Indicators
- **Clip Count**: 5 clips per video (target)
- **Processing Success**: >95% completion rate
- **Encoding Quality**: No artifacts or corruption
- **Subtitle Accuracy**: >90% word accuracy
- **Framing Accuracy**: Proper aspect ratio detection

### Performance Benchmarks
- **5-minute video**: <5 minutes processing
- **15-minute video**: <15 minutes processing
- **30-minute video**: <30 minutes processing
- **GPU utilization**: >80% during encoding
- **Error rate**: <5% failed executions

## 🚀 Complete Deployment Checklist

### Phase 1: Environment Setup
- [ ] Windows 10/11 with WSL2 or Linux system
- [ ] Docker Desktop installed and running
- [ ] NVIDIA GPU drivers installed (latest)
- [ ] Docker GPU support verified: `docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi`
- [ ] 50GB+ free disk space available

### Phase 2: File Creation
- [ ] Project directory created: `IAV_Core_Production_Engine/`
- [ ] docker-compose.yaml file created with all 4 services
- [ ] Dockerfile.n8n created with FFmpeg and yt-dlp
- [ ] config.env created with your credentials
- [ ] deploy.sh created and made executable
- [ ] workflow.json downloaded/created
- [ ] Utility scripts created in scripts/ directory

### Phase 3: Deployment
- [ ] Run `./deploy.sh` successfully
- [ ] All 4 containers healthy: `docker-compose ps`
- [ ] n8n accessible at http://localhost:5678
- [ ] Ollama API responding at http://localhost:11434
- [ ] AI models downloaded (LLaMA 3.1 8B + Whisper)

### Phase 4: Configuration
- [ ] n8n account created with config.env credentials
- [ ] 32-character API key generated from Settings → API Keys
- [ ] config.env updated with API key
- [ ] workflow.json imported via n8n UI
- [ ] Workflow activated (toggle switch ON)
- [ ] Webhook registered: `curl http://localhost:5678/webhook-test/iav` returns 404

### Phase 5: Testing
- [ ] Test webhook: `./scripts/test-webhook.sh`
- [ ] Processing completes without errors
- [ ] 5 clips generated in `/data/YYYY-MM-DD/IAV_Core_Production_Engine/mp4/`
- [ ] Clips are 1080x1920 MP4 format with embedded subtitles
- [ ] manifest.jsonl contains 5 entries with metadata

### Phase 6: Validation
- [ ] GPU encoding active during processing: `nvidia-smi dmon`
- [ ] Clips play correctly in video player
- [ ] Search functionality works: `./scripts/findclip test`
- [ ] System resources within acceptable limits
- [ ] Logs show no critical errors

## 🎯 Quick Start Summary

For experienced users, here's the minimal setup:

```bash
# 1. Create project and files
mkdir IAV_Core_Production_Engine && cd IAV_Core_Production_Engine
# Copy all YAML, Dockerfile, and script contents from this guide

# 2. Deploy
chmod +x deploy.sh && ./deploy.sh

# 3. Configure
# - Open http://localhost:5678
# - Create account, get API key
# - Import workflow.json, activate

# 4. Test
curl -X POST "http://localhost:5678/webhook/iav" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://youtube.com/watch?v=dQw4w9WgXcQ"}'

# 5. Verify
ls data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine/mp4/
```

Expected result: 5 MP4 clips ready for social media upload.

## 📞 Support & Maintenance

### Regular Maintenance Tasks
- **Daily**: Monitor disk usage, check processing logs
- **Weekly**: Run cleanup scripts, backup important clips
- **Monthly**: Update yt-dlp, rotate API keys, review performance metrics

### Performance Optimization
- Use SSD storage for `/data` directory
- Increase Docker memory limits for large videos
- Monitor GPU temperature during heavy processing
- Consider dedicated GPU server for high-volume usage

### Backup Strategy
- Backup `/data` directory regularly
- Export workflow JSON monthly
- Save configuration files to version control
- Document any custom modifications

---

*This comprehensive guide provides everything needed to build, deploy, and operate the IAV Core Production Engine from scratch. The system is designed for reliability, scalability, and ease of use in production environments.*
