{"name": "IAV Core Production Engine Fixed", "nodes": [{"parameters": {"httpMethod": "POST", "path": "iav-fixed", "responseMode": "responseNode", "responseNode": "Prepare Response", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "webhook-node", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1.1, "position": [250, 300], "webhookId": "8f7e4d3c-9b2a-4f5e-8c1d-0a6b5e3f7d2c"}, {"parameters": {"command": "=echo \"Starting video processing for: {{$json.url}}\""}, "id": "log-start", "name": "Log Start", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"command": "=mkdir -p /data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine/{mp4,metadata,logs}"}, "id": "create-dirs", "name": "Create Directories", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"command": "=cd /data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine && yt-dlp -f 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best' --merge-output-format mp4 -o 'source_video.mp4' '{{$json.url}}' --quiet --no-warnings && echo \"Download complete\""}, "id": "download-video", "name": "Download Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"command": "=cd /data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine && ffprobe -v quiet -print_format json -show_format -show_streams source_video.mp4 > metadata/video_info.json && cat metadata/video_info.json"}, "id": "get-video-info", "name": "Get Video Info", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"jsCode": "// Parse video metadata\nconst videoInfo = JSON.parse($input.first().json.stdout);\nconst duration = parseFloat(videoInfo.format.duration);\n\n// Generate 5 clips\nconst clips = [];\nconst clipDuration = 60;\n\n// Strategic points in the video\nconst clipPoints = [0.1, 0.3, 0.5, 0.7, 0.9];\n\nfor (let i = 0; i < 5; i++) {\n  const startTime = Math.floor(duration * clipPoints[i]);\n  const endTime = Math.min(startTime + clipDuration, duration);\n  \n  clips.push({\n    clipNumber: i + 1,\n    startTime: startTime,\n    endTime: endTime,\n    duration: endTime - startTime,\n    outputFile: `clip_${i + 1}_viral_moment_${i + 1}.mp4`\n  });\n}\n\nreturn clips;"}, "id": "generate-clips", "name": "Generate Clip List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}, {"parameters": {"command": "=cd /data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine && ffmpeg -i source_video.mp4 -ss {{$json.startTime}} -t {{$json.duration}} -c:v libx264 -preset fast -crf 23 -c:a aac -b:a 192k -movflags +faststart mp4/{{$json.outputFile}} -y && echo \"Encoded: {{$json.outputFile}}\""}, "id": "encode-clips", "name": "Encode Clips", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1450, 300]}, {"parameters": {"command": "=cd /data/$(date +%Y-%m-%d)/IAV_Core_Production_Engine && echo '{\"clip\": \"{{$json.outputFile}}\", \"start\": {{$json.startTime}}, \"duration\": {{$json.duration}}, \"timestamp\": \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}' >> manifest.jsonl"}, "id": "update-manifest", "name": "Update Manifest", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1650, 300]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "message", "value": "Video processing complete"}, {"name": "output_directory", "value": "/data/{{$now.format('yyyy-MM-dd')}}/IAV_Core_Production_Engine"}], "number": [{"name": "clips_generated", "value": 5}]}, "options": {}}, "id": "prepare-response", "name": "Prepare Response", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [1850, 300]}], "connections": {"Webhook": {"main": [[{"node": "Log Start", "type": "main", "index": 0}]]}, "Log Start": {"main": [[{"node": "Create Directories", "type": "main", "index": 0}]]}, "Create Directories": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Get Video Info", "type": "main", "index": 0}]]}, "Get Video Info": {"main": [[{"node": "Generate Clip List", "type": "main", "index": 0}]]}, "Generate Clip List": {"main": [[{"node": "Encode Clips", "type": "main", "index": 0}]]}, "Encode Clips": {"main": [[{"node": "Update Manifest", "type": "main", "index": 0}]]}, "Update Manifest": {"main": [[{"node": "Prepare Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveExecutionProgress": true, "saveDataSuccessExecution": "all", "saveManualExecutions": true}, "staticData": {}, "tags": []}